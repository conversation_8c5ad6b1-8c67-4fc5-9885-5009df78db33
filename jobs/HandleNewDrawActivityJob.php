<?php

namespace app\jobs;

use app\components\AppNRedisKeys;
use app\components\EventMsg;
use app\exceptions\DrawActivityException;
use app\models\by;
use app\models\byNew;
use app\models\CUtil;
use app\modules\goods\models\DrawExternalCouponModel;

/**
 * 处理抽奖任务
 */
class HandleNewDrawActivityJob extends BaseJob
{
    // 失败重试3次
    protected $attempt = 3;

    // 用户ID
    public $user_id;
    // 用户手机号
    public $user_phone;
    // 活动ID
    public $activity_relation_id;
    public $prize;

    /**
     * @param $queue
     * @return void
     */
    public function execute($queue)
    {
        // 替换奖品
        $prizeData['value'] = $this->prize['link'] ?? '';
        $prizeData['name']  = $this->prize['name'] ?? '';

        // 2、处理数据
        $prizeRecordId = $this->handle($this->user_id, $this->user_phone, $this->activity_relation_id, $prizeData);
        if (!$prizeRecordId) {
            return;
        }
    }


    /**
     * 添加领取记录
     * @param  $user_id
     * @param  $user_phone
     * @param  $acRelationId
     * @param  $prizeData
     * @return int
     */
    public function handle($user_id, $user_phone, $acRelationId, $prizeData): int
    {

        $db    = by::dbMaster();
        $redis = by::redis();
        // 1、执行更新操作
        $transaction = $db->beginTransaction();
        try {
            $now = time();
            // 插入中奖记录
            $prizeRecordId   = 0;
            $envelope_record = [
                    'activity_relation_id' => $acRelationId,
                    'user_id'              => $user_id,
                    'user_phone'           => $user_phone,
                    'prize_name'           => $prizeData['name'],
                    'prize_value'          => $prizeData['value'], // 奖品值：当是第三方券时，展示具体的券码
                    'collect_time'         => $now,
                    'ctime'                => $now,
            ];
            $db->createCommand()->insert(byNew::EnvelopeRecord()::tbName(), $envelope_record)->execute();
            // 获取中奖记录id
            $prizeRecordId = $db->getLastInsertID();

            $transaction->commit();

            // 清除中奖记录缓存
            $redisKey = AppNRedisKeys::newDrawActivityRecord($acRelationId);
            $subKey   = CUtil::getNewAllParams("users", $user_id);

            $redis->hdel($redisKey, $subKey);

            return $prizeRecordId;
        } catch (\Exception $e) {
            $transaction->rollBack();
            // 异常日志
            $this->logError("处理数据异常：参数：user_id:{$user_id}, activity_relation_id:{$acRelationId}；异常：file:{$e->getFile()}, line:{$e->getLine()}, code:{$e->getCode()}, msg:{$e->getMessage()}");
        }
        return 0;
    }

    private function logError(string $message)
    {
        CUtil::debug($message, 'err.envelope-activity.job');
    }

}