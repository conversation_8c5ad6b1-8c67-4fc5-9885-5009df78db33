<?php

namespace app\models;

use app\components\AliYunSls;
use app\components\ErrCode;
use app\components\Kafka;
use app\components\TcpClient;
use app\components\UdpClient;
use app\constants\RespStatusCodeConst;
use app\jobs\AliYunLogJob;
use app\modules\back\services\system\SystemDictDataService;
use app\modules\main\forms\BaseModel;
use app\modules\main\models\CommModel;
use app\models\CodeModel;
use Redis;
use Yii;
use yii\db\Exception;

/**
 * 小功能接口类
 */
class CUtil
{

    public static $instance = [];

    // 正则表达式类型
    const REG_PHONE = 0;        // 手机号码
    const REG_VERIFYCODE = 1;        // 手机验证码
    const REG_EMAIL = 2;        // 电子邮箱
    const REG_PASSWORD = 3;        // 交易密码（32位md5值）
    const REG_LANIP = 4;        // 内网IP地址串
    const REG_CDKEY = 5;        // CDKEY（32个字节16进制串）
    const REG_ORDERID = 6;        // 订单ID
    const REG_USERID = 7;        // 用户ID
    const REG_ADDRESS = 8;        // 收货地址
    const URL = 9;        // 判断是否url
    const REG_SN = 10;        // 判断是否sn

    /**
     * 检查字符串格式是否正确
     * @param $str : 字符串
     * @param $type : 字符串类型
     * @return true: 格式正确，fasle不正确
     */
    static public function reg_valid($str, $type)
    {

        switch ($type) {
            case self::REG_PHONE:
                //$pattern = '/^(11|12|13|14|15|16|17|18|19)[\d]{9}$/';
                $pattern = '/^1[3456789]\d{9}$/';
                break;
            case self::REG_VERIFYCODE:
                $pattern = '/^[a-zA-Z0-9]{1,32}$/';
                break;
            case self::REG_EMAIL:
                $pattern = '/^[a-zA-Z0-9_\-\.]+@[a-zA-Z0-9_-]+(\.[a-zA-Z0-9_-]+)+$/';
                break;
            case self::REG_PASSWORD:
                $pattern = '/^[a-zA-Z0-9]{32}$/';
                break;
            case self::REG_LANIP:
                $pattern = '/^((192\.168|172\.([1][6-9]|[2]\d|3[01]))(\.([2][0-4]\d|[2][5][0-5]|[01]?\d?\d)){2}|10(\.([2][0-4]\d|[2][5][0-5]|[01]?\d?\d)){3})$/';
                break;
            case self::REG_CDKEY:
                $pattern = '/^BM[a-zA-Z0-9]{32}$/i';
                break;
            case self::REG_ORDERID:
                $pattern = '/^[a-zA-Z0-9]{1,30}$/';
                break;
            case self::REG_USERID:
                $pattern = '/^\d{1,30}$/';
                break;
            case self::REG_ADDRESS:
                $pattern = '/select|update|delete|exec|count|"|=|;|>|<|%/i';
                break;
            case self::URL:
                $pattern = "/^http(s?):\/\/(?:[A-za-z0-9-]+\.)+[A-za-z]{2,4}(?:[\/\?#][\/=\?%\-&~`@[\]\':+!\.#\w]*)?$/";
                break;
            case self::REG_SN:
                $pattern = "/^[a-zA-Z0-9\/-]{1,30}$/";
                break;
            default:
                return false;
        }

        if (!preg_match($pattern, $str)) {
            return false;
        } else {
            return true;
        }
    }

    /**
     * @param string $ip
     * @param int $type
     * @return mixed
     * 是否是有效IP
     */
    public static function ipIsLegal($ip = '', $type = 0)
    {
        switch ($type) {
            case 1 :
                //判断是否是合法的IPv4 IP地址
                $ret = filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_IPV4);
                break;
            case 2 :
                //判断是否是合法的公共IPv4地址，***********这类的私有IP地址将会排除在外
                $ret = filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_IPV4 | FILTER_FLAG_NO_PRIV_RANGE);
                break;
            case 3 :
                //判断是否是合法的IPv6地址
                $ret = filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_NO_RES_RANGE);
                break;
            case 4 :
                //判断是否是public IPv4 IP或者是合法的Public IPv6 IP地址
                $ret = filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE);
                break;
            case 0 :
            default:
                //判断是否是合法IP
                $ret = filter_var($ip, FILTER_VALIDATE_IP);
                break;
        }
        return $ret;
    }

    /**
     * @param $url
     * @param int $timeout :超时时间(s)
     * @param null $headers
     * @param string $cookie
     * @param string $body
     * @return bool|string
     * 发起get请求
     */
    static public function curl_get($url, $timeout = 10, $headers = null, $cookie = "", $body = "", $ddw = false)
    {
        $func = __FUNCTION__;
        $start = microtime(TRUE);
        $cl = curl_init($url);

        curl_setopt($cl, CURLOPT_ENCODING, 'UTF-8');
        curl_setopt($cl, CURLOPT_TIMEOUT, $timeout);
        curl_setopt($cl, CURLOPT_RETURNTRANSFER, true); // 获取数据返回

        curl_setopt($cl, CURLOPT_SSL_VERIFYPEER, false);
        //本地调试curl取消ssl检查
        !YII_ENV_PROD && curl_setopt($cl, CURLOPT_SSL_VERIFYPEER, FALSE);
        if (!empty($headers)) {
            curl_setopt($cl, CURLOPT_HTTPHEADER, $headers);
        }
        if (!empty($cookie)) {
            curl_setopt($cl, CURLOPT_COOKIE, $cookie);
        }
        if (!empty($body)) {
            curl_setopt($cl, CURLOPT_CUSTOMREQUEST, 'GET');
            curl_setopt($cl, CURLOPT_POSTFIELDS, $body);
        }

        $result = curl_exec($cl);
        if (curl_errno($cl)) { // 发生错误
            CUtil::writeLog($func, 99, 'curl error: ' . curl_error($cl) . ',url:' . $url, microtime(TRUE) - $start, 'error', __METHOD__);
        }
        if ($ddw) {
            return $result;
        }
        $httpCode = @curl_getinfo($cl, CURLINFO_HTTP_CODE);
        curl_close($cl);
        if ($httpCode == "0") {
            // Time out
            //throw new Exception("Curl error number:" . $curlErrNo . " , Curl error details:" . $curlErr );
            return FALSE;
        } else if ($httpCode != "200") {
            // We did send the notifition out and got a non-200 response
            //throw new Exception("Http code:" . $httpCode .  " details:" . $result);
            return FALSE;
        } else {
            return $result;
        }
    }

    /**
     * @param $url
     * @param $body :post数据
     * @param null $headers :头部信息数组,如：array("User-Agent: NuSOAP/0.6.6',"Content-Type: text/xml; charset=UTF-8");
     * @param int $timeout : 超时时间(s)
     * @param bool $bhttps : https验证
     * @param string $ipProtocol : ip协议
     * @param string $useCert : wx:使用微信证书  qq:使用微信证书
     * @param bool $ddw : 是否直接返回值
     * @return bool|string
     * 发起post请求
     */
    public static function curl_post($url, $body, $headers = null, $timeout = 10, $bhttps = FALSE, $ipProtocol = '', $useCert = '', $ddw = false)
    {
        $func = __FUNCTION__;
        $start = microtime(TRUE);
        $cl = curl_init($url);

        curl_setopt_array($cl, array(
            CURLOPT_POSTFIELDS => $body,
            CURLOPT_TIMEOUT => $timeout,
            CURLOPT_RETURNTRANSFER => 1
        ));

        if ($bhttps) {
            curl_setopt($cl, CURLOPT_SSL_VERIFYPEER, FALSE);    // https请求 不验证证书和hosts
            curl_setopt($cl, CURLOPT_SSL_VERIFYHOST, FALSE);
        }

        //ip协议设置
        switch ($ipProtocol) {
            case 'IPV4' :
                curl_setopt($cl, CURLOPT_IPRESOLVE, CURL_IPRESOLVE_V4);
                break;
            case 'IPV6':
                curl_setopt($cl, CURLOPT_IPRESOLVE, CURL_IPRESOLVE_V6);
                break;
            default:
                break;
        }

        if (!empty($headers)) {
            curl_setopt($cl, CURLOPT_HTTPHEADER, $headers);
        }

        if ($useCert) {
            //设置证书
            //使用证书：cert 与 key 分别属于两个.pem文件
            //证书文件请放入服务器的非web目录下
            $sslCertPath = "";
            $sslKeyPath = "";
            if ($useCert == 'wx') {
                by::wxpay()->GetSSLCertPath($sslCertPath, $sslKeyPath);
            }elseif ($useCert == 'wxh5'){
                by::wxH5Pay()->GetSSLCertPath($sslCertPath, $sslKeyPath);
            }
            curl_setopt($cl, CURLOPT_SSLCERTTYPE, 'PEM');
            curl_setopt($cl, CURLOPT_SSLCERT, $sslCertPath);
            curl_setopt($cl, CURLOPT_SSLKEYTYPE, 'PEM');
            curl_setopt($cl, CURLOPT_SSLKEY, $sslKeyPath);
        }

        $result = curl_exec($cl);

        if (curl_errno($cl)) { // 发生错误
            CUtil::writeLog($func, 99, 'curl errno:' . curl_errno($cl) . ', error: ' . curl_error($cl) . ',url:' . $url, microtime(TRUE) - $start, 'error', __METHOD__);
        }

        $httpCode = @curl_getinfo($cl, CURLINFO_HTTP_CODE);

        curl_close($cl);

        if ($ddw) {
            return $result;
        }

        if ($httpCode == "0") {
            //throw new Exception("Curl error number:" . $curlErrNo . " , Curl error details:" . $curlErr );
            CUtil::debug('请求异常：请求地址：' . $url . '请求体：' . $body . '状态码：' . $httpCode . '响应结果：' . serialize($result), 'err.request');
            return FALSE;
        } else if ($httpCode != "200") {
            // We did send the notifition out and got a non-200 response
            //throw new Exception("Http code:" . $httpCode .  " details:" . $result);
            CUtil::debug('请求异常：请求地址：' . $url . '请求体：' . $body . '状态码：' . $httpCode . '响应结果：' . serialize($result), 'err.request');
            return FALSE;
        } else {
            return $result;
        }
    }

    /**
     * @param $url
     * @param $body :post数据
     * @param null $headers : 头部信息数组,如：array("User-Agent: NuSOAP/0.6.6',"Content-Type: text/xml; charset=UTF-8");
     * @param int $timeout :超时时间(s)
     * @return array
     * 发起post请求,并将curl errno透传给调用方
     */
    public static function curl_post_notify_err($url, $body, $headers = null, $timeout = 10)
    {
        $func = __FUNCTION__;
        $start = microtime(TRUE);

        $cl = curl_init($url);
        curl_setopt_array($cl, array(
            CURLOPT_POSTFIELDS => $body,
            CURLOPT_TIMEOUT => $timeout,
            //CURLOPT_TIMEOUT_MS => 100,
            CURLOPT_RETURNTRANSFER => 1
        ));
        if (!empty($headers)) {
            curl_setopt($cl, CURLINFO_HEADER_OUT, TRUE);
            curl_setopt($cl, CURLOPT_HTTPHEADER, $headers);
        }
        $result = curl_exec($cl);
        $err = curl_errno($cl); //http://php.net/manual/zh/function.curl-errno.php
        if ($err) { // 发生错误
            CUtil::writeLog($func, 99, 'curl error: ' . $err . ',url:' . $url, microtime(TRUE) - $start, 'error', __METHOD__);
        }

        $a = curl_getinfo($cl);


        curl_close($cl);
        return array('r' => $err, 'ret' => $result, 'info' => $a);
    }

    /**
     * @param int $len : 验证码长度 最小1位
     * @param int $type :0 纯数字类型，1:字符串型
     * @return bool|int|string
     * 生成验证码
     */
    static public function createVerifyCode($len = 1, $type = 0)
    {
        if ($type == 0) {
            $len = intval($len);
            $verifyCode = false;
            if ($len > 0) {
                $min = pow(10, $len - 1);
                $max = pow(10, $len) - 1;
                $verifyCode = rand($min, $max);
            }
        } else {
            $str = "0,1,2,3,4,5,6,7,8,9,A,B,C,D,E,F,G,H,I,J,K,L,M,N,O,P,Q,R,S,T,U,V,W,X,Y,Z";
            $list = explode(",", $str);
            $cmax = count($list) - 1;
            $verifyCode = '';
            for ($i = 0; $i < $len; $i++) {
                $randnum = mt_rand(0, $cmax);
                $verifyCode .= $list[$randnum];
            }
        }

        return $verifyCode;
    }

    /**
     * 记录日志
     * @param $func : 接口名称
     * @param $ret : 返回码
     * @param $msg : 详细信息
     * @param $time : 调用接口所花时间
     * @param $level : 日志等级
     * @param $toUdp bool : 默认UDP服务器转发，否则记录数数据库
     * @param $category : 日志分类
     */
    static public function writeLog($func, $ret, $msg, $time, $level, $category, $toUdp = true)
    {
        //改由UDP服务器实时通知
        $toUdp && trigger_error("{$category}|{$msg}", E_USER_ERROR);

        $logMsg = "{\"f\":\"$func\",\"r\":\"$ret\",\"rd\":\"$msg\",\"t\":$time}";
        try {
            //Yii::log(json_encode($logMsg), $level, $category);
            if ($level == 'trace') {
                Yii::trace($logMsg, $category);
            } elseif ($level == 'info') {
                Yii::info($logMsg, $category);
            } elseif ($level == 'warning') {
                Yii::warning($logMsg, $category);
            } elseif ($level == 'error') {
                Yii::error($logMsg, $category);
            }
        } catch (\Exception $ec) {
            //Yii::log(json_encode($logMsg), $level);
            Yii::error($logMsg, $category);
        }
    }

    /**
     * @param $type
     * @param $name
     * @param int $default
     * @return array|int|mixed|null
     * 获取参数
     */
    static public function getRequestParam($type, $name, $default = 0)
    {
        $data = NULL;
        $request = Yii::$app->request;
        switch ($type) {
            case "cookie":
                if (isset($request->cookies[$name])) {
                    $data = $request->cookies[$name]->value;
                } else {
                    $data = $default;
                }
                break;
            case "get":
                $data = $request->get($name, $default);
                break;
            case "post":
                $data = $request->post($name, $default);
                break;
            case "headers":
                $data = $request->headers->get($name, $default);
                break;
            default: //先获取post参数，如果为空，再获取get参数。
                $data = $request->post($name, $default);
                if (empty($data)) {
                    $data = $request->get($name, $default);
                }
        }
        return $data;
    }

    //获取当前毫秒数
    static public function getMillisecond(): float
    {
        list($s1, $s2) = explode(' ', microtime());
        return (float)sprintf('%.0f', (floatval($s1) + floatval($s2)) * 1000);
    }

    //获取当前微秒数
    static public function getMicrosecond(): float
    {
        list($s1, $s2) = explode(' ', microtime());
        return (float)sprintf('%.0f', (floatval($s1) + floatval($s2)) * 1000000);
    }

    //获取当前纳秒数
    static public function getNanosecond(): string
    {
        list($s1, $s2) = explode(' ', microtime());
        return sprintf('%.0f', (doubleval($s1) + doubleval($s2)) * 1000000000);
    }

    /**
     * 获得请求客户端信息
     * @return number 0 未知 1ios 2android
     */
    static public function getOrigin(): int
    {
        if (strpos($_SERVER['HTTP_USER_AGENT'], 'iPhone') || strpos($_SERVER['HTTP_USER_AGENT'], 'iPad')) {
            return 1;
        } else if (strpos($_SERVER['HTTP_USER_AGENT'], 'Android')) {
            return 2;
        } else {
            return 0;
        }
    }

    /**
     * 获取客户端 ip 地址
     * @return string
     */
    public static function get_client_ip(): string
    {
        $realip = '';

        if (isset($_SERVER)) {
            if (isset($_SERVER["HTTP_X_FORWARDED_FOR"])) {
                $realip = $_SERVER["HTTP_X_FORWARDED_FOR"];
                //Get the first IP from X-Forwarded-For: IP1, IP2, IP3
                $iparr = explode(',', $realip);
                $realip = trim($iparr[0]);
            } elseif (isset($_SERVER["HTTP_CLIENT_IP"])) {
                $realip = $_SERVER["HTTP_CLIENT_IP"];
            } elseif (isset($_SERVER['HTTP_X_REAL_IP'])) {
                $realip = $_SERVER['HTTP_X_REAL_IP'];
            } elseif (isset($_SERVER["REMOTE_ADDR"])) {
                $realip = $_SERVER["REMOTE_ADDR"];
            }
        } else {
            if (getenv('HTTP_X_FORWARDED_FOR')) {
                $realip = getenv('HTTP_X_FORWARDED_FOR');
                //Get the first IP from X-Forwarded-For: IP1, IP2, IP3
                $iparr = explode(',', $realip);
                $realip = trim($iparr[0]);
            } elseif (getenv('HTTP_CLIENT_IP')) {
                $realip = getenv('HTTP_CLIENT_IP');
            } elseif (getenv('HTTP_X_REAL_IP')) {
                $realip = getenv('HTTP_X_REAL_IP');
            } elseif (getenv('REMOTE_ADDR')) {
                $realip = getenv('REMOTE_ADDR');
            }
        }
        return $realip;
    }

    public static function is_ip_in_range($ip,$ip_str): bool
    {
        $mark_len = 32;
        if (strpos($ip_str, "/") > 0) {
            list($ip_str, $mark_len) = explode("/", $ip_str);
        }
        $right_len = 32 - $mark_len;
        return ip2long($ip) >> $right_len == ip2long($ip_str) >> $right_len;
    }


    /**
     * @param int $iRet
     * @param string $msg
     * @param array $data
     * @return false|string
     * 系统标准json
     */
    public static function SystemStandardJson($iRet = 0, $msg = '', $data = [])
    {

        $json_arr = ['iRet' => $iRet, 'sMsg' => $msg, 'data' => $data];

        return json_encode($json_arr);
    }

    /**
     * @param int $iRet
     * @param string $msg
     * @param array $data
     * @param bool $exit
     * 统一json格式返回
     * http://www.bejson.com/
     */
    public static function json_response($iRet = 0, $msg = '', $data = [], $exit = true)
    {

        // CLI 模式无需设置 header
        if (PHP_SAPI != 'cli') {
            header('Content-Type: application/json');
        }

        // 防止循环输出
        static $response = true;
        if ($response === false) {
            exit(0);
        }

        // 获取错误信息
        $msg = $msg ?: RespStatusCodeConst::getMessageByCode($iRet);
        if (empty($msg)){
            $msg = RespStatusCodeConst::getMessageByCode($iRet);
        }


        // 生成 JSON 响应
        $json = self::SystemStandardJson($iRet, $msg, $data);

        // 记录日志
        if (Yii::$app instanceof \yii\web\Application) {
            // 输出响应
            echo $json;
            $response = false;
            // 防止重复响应
            self::setLogMsg(
                    'info.api.request_web', Yii::$app->request->post(), $data, Yii::$app->request->headers->toArray(), Yii::$app->request->getPathInfo(), $msg, []
            );
        } else {
            $response = false;
            self::setLogMsg(
                    'info.api.request', [], [], [], '', $msg, []
            );
        }

        // 数据收集
//        by::model('CommModel', MAIN_MODULE)->recordPostLog([
//                'start_time' => START_TIME,
//                'iRet'       => $iRet,
//                'sMsg'       => $msg,
//                'end_time'   => microtime(true),
//        ]);

        // 根据参数决定是否退出
        $exit && exit(0);
    }



    /**
     * 账号中心返回数据
     * @param $code
     * @param $msg
     * @param array $data
     * @param array $centerHeader
     * @param bool $exit
     * @return void
     */
    public static function json_center_response($code, $msg, array $centerHeader = [], array $data = [], bool $exit = true)
    {
        //cli模式无需设置header头
        if (PHP_SAPI != 'cli') {
            header('Content-Type: application/json');
        }
        // todo 防止循环输出
        static $response = true;
        if ($response == false) {
            exit(0);
        }
        if ($centerHeader) $responseData['header'] = $centerHeader;
        $responseData['payload'] = ['code' => $code, 'msg' => $msg, 'data' => $data];
        echo json_encode($responseData);
        $response = false;
        //数据收集
//        by::model('CommModel', MAIN_MODULE)->recordPostLog([
//            'start_time' => START_TIME,
//            'iRet' => $code == 200 ? 1 : 0,
//            'sMsg' => $msg,
//            'end_time' => microtime(true),
//        ]);
        $exit && exit(0);
    }

    /**
     * dreamehome 返回参数封装
     * @param int $code
     * @param string $msg
     * @param array $data
     * @param bool $success
     * @param bool $exit
     * @return void
     */
    public static function json_app_response(int $code, string $msg = '', array $data = [], bool $success = false, bool $exit = true)
    {
        //cli模式无需设置header头
        if (PHP_SAPI != 'cli') {
            header('Content-Type: application/json');
        }
        // todo 防止循环输出
        static $response = true;
        if ($response == false) {
            exit(0);
        }
        if ($code === 0) $success = true;
        $responseData = ['code' => $code, 'success' => $success, 'data' => $data, 'msg' => $msg];
        echo json_encode($responseData);
        $response = false;
        //数据收集
//        by::model('CommModel', MAIN_MODULE)->recordPostLog([
//            'start_time' => START_TIME,
//            'iRet' => $code === 0 ? 1 : 0,
//            'sMsg' => $msg,
//            'end_time' => microtime(true),
//        ]);
        $exit && exit(0);
    }


    /**
     * @param $dir
     * @param $fileName
     * @param string $module
     * @param string $afterfix
     * @return string
     * 获取指定目录指定文件
     */
    public static function getFilePath($dir, $fileName, $module = '', $afterfix = ".php"): string
    {
        $dir = strtolower($dir);
        $fileName = strtolower($fileName);
        $path = Yii::$app->basePath;
        $module = $module ? $module : Yii::$app->controller->module->id;

        //加载子模块
        if ((strstr($fileName, 'my_') !== false) && (strtolower($module) != strtolower(Yii::$app->id))) {
            return "{$path}/modules/{$module}/{$dir}/{$fileName}{$afterfix}";
        }

        //加载主目录模块
        return "{$path}/{$dir}/{$fileName}{$afterfix}";
    }


    /**
     * @param string $key
     * @param string $name
     * @param string $module
     * @param bool $cache
     * @return array|string
     * 获取配置文件
     */
    public static function getConfig($key = '', $name = 'common', $module = '', $cache = true)
    {
        clearstatcache();

        $file_name = __FUNCTION__ . "|{$name}|$module";

        if (!$cache) {
            unset(self::$instance[$file_name]);
        }
        if (empty(self::$instance[$file_name])) {
            $module = $module ? $module : Yii::$app->controller->module->id;

            if ($module && (strtolower($module) != strtolower(Yii::$app->id))) {
                $file = self::getFilePath('config', "my_{$name}", $module);
            } else {

                $file = self::getFilePath('config/' . YII_ENV, $name, $module);
            }

            if (is_file($file)) include $file;

            if (!isset($config) || !is_array($config)) {
                return '';
            }

            self::$instance[$file_name] = $config;
        }

        return self::$instance[$file_name][$key] ?? [];
    }

    /**
     * @param $dir
     * @param $name :类名 (非当前模块类，需要指定命名空间路径)
     * @param null $param
     * @param null $param1
     * @param null $module
     * @return mixed
     * 优先获取当前模块下的类 然后获取指定路径的类
     */
    public static function getClass($dir, $name, $module = null, $param = null, $param1 = null)
    {

        if (empty($dir) || empty($name)) {
            trigger_error("class err!", E_USER_ERROR);
        }

        $module = $module ? $module : Yii::$app->controller->module->id;
        $className = "app\modules\\{$module}\\{$dir}\\{$name}";
        //优先加载当前类
        if (!class_exists($className)) {
            $className2 = "app\\{$dir}\\{$name}";
            if (!class_exists($className2)) {
                $className3 = $name;
                if (!class_exists($className3)) {
                    trigger_error("{$className3} Not Found !", E_USER_ERROR);
                } else {
                    $className = $className3;
                }
            } else {
                $className = $className2;
            }
        }

        return new $className($param, $param1);
    }


    /**
     * @param $num
     * @param int $min
     * @return mixed
     * 整形数字边界值合法验证
     */
    public static function uint($num, $min = 0)
    {
        return max(intval($min), intval($num));
    }

    /**
     * 版本号字符串转换为数字
     * @param string $version 版本号(格式: 3.1.2)
     * @return int 数字化的版本号
     */
    public static function version2long($version = '1.0.0')
    {
        if ($version == '') return '';
        if (false === strpos($version, '.')) {
            return $version;
        }
        list($first, $middle, $last) = explode('.', $version);
        $first = intval($first);
        $middle = min(intval($middle), 999);
        $last = min(intval($last), 999);
        return ($first * 1000000 + $middle * 1000 + $last);
    }

    /**
     * 版本号数字转换为字符串
     *
     * @param int $verint 版本号数字
     * @return string 版本号字符串
     */
    public static function long2version(int $verint): string
    {
        $verint = intval($verint);
        $first = floor($verint / 1000000);
        $middle = floor(($verint % 1000000) / 1000);
        $last = $verint % 1000;
        return "{$first}.{$middle}.{$last}";
    }

    /**
     * 版本号比较
     *
     * @param string $ver1 版本号1(格式需要如下：1.0.1)
     * @param string $ver2 版本号2(格式需要如下：1.0.1)
     * @return int 比较结果 0:版本相同 大于0:$ver1大于$ver2 小于0:$ver1小于$ver2
     */
    public static function versionCompare(string $ver1, string $ver2)
    {
        return self::version2long($ver1) - self::version2long($ver2);
    }

    /**
     * @param $arr
     * @return bool|int|string
     * 按照概率抽奖
     * $arr = [ 1=>0.30095, 2=>0.00003, 3=>0.00002, 4=>0.699 ] (最大精度0.001%)
     */
    public static function getLuckyKey($arr)
    {
        if (empty($arr) || !is_array($arr)) {
            return false;
        } else {
            $scale = 5;//精确到万分之一
            $max = 100000;
            $t_end = 0;
            $check = 0;
            $tmp = [];
            foreach ($arr as $key => $value) {
                $value = number_format($value, $scale, '.', '');//float to string
                $check = bcadd($check, $value, $scale);//$check用来判断概率总和是否小于1
                $begin = $t_end + 1;
                $t_end = bcadd($t_end, bcmul($value, $max));//记录本层的结束位置
                $tmp[$key]['rate_begin'] = $begin;
                $tmp[$key]['rate_end'] = $t_end;
            }

            if (bccomp($check, 1, $scale) != 0) {
                return false;//总概率不等于1，直接返回false
            }

            $random = mt_rand(1, $max);
            foreach ($tmp as $key => $value) {
                if ($random >= $value['rate_begin'] && $random <= $value['rate_end']) {
                    $chose = $key;
                    break;
                }
            }

            return isset($chose) ? $chose : false;
        }
    }


    /**
     * @param string $string : 日志内容
     * @param string $filename : 文件名 最大20字符
     * @param string $protocol :发送日志传输协议 0 UDP , 1 TCP
     * 文件日志系统（正式服日志发往日志服务器，开发测试服打印在本地）
     * 说明：日志系统最终生成的日志在【LogServerPath】/Web/年月/项目名/日期/文件名.log
     * 访问方式：例如想查询2021年09月，QQHall项目15号的，文件名为kevin.lin的日志内容,
     * 则日志路径为：[host]/web/source/[账号]/[密码]?file=202109/QQHall/15/kevin.lin
     * [host],[账号],[密码] 请从 WIKI 获取最新的配置
     * http://oa.crosscp.com/dokuwiki/doku.php?id=%E5%85%AD%E8%B1%A1-%E6%97%A5%E5%BF%97%E6%9C%8D%E5%8A%A1
     * 重要说明：请勿将日志服务器当数据库使用，禁止无限死循环打印日志！！！！
     */
    public static function debug($string = '', $filename = '',$filePath = '', $protocol = 0)
    {
         // 测试环境不调用sls
        if (YII_ENV_DEV || YII_ENV_LOCAL) {
            self::saveLocalLog($filename, $string);
            return true;
        }
        // 临时处理日志结构调整
        if (Yii::$app instanceof \yii\web\Application) {
            $string = self::setLogMsg(
                $filename,
                Yii::$app->request->post() ?? [],
                [],
                Yii::$app->request->headers->toArray() ?? [],
                Yii::$app->request->getPathInfo() ?? '',
                'short_log',
                ['msg'=>$string],
                200,
                2
            );
        }else{
            $string = self::setLogMsg(
                $filename,
                [],
                [],
                [],
                '',
                'commands',
                ['msg'=>$string],
                200,
                2
            );
        }
        if(!$filePath){
            $backtrace = debug_backtrace();
            if (isset($backtrace[1]) && !empty($backtrace[1])) {
                $filePath = $backtrace[1]['file'] ?? 'Unknown file';
            }
        }
        self::logSendKafka($filename, $string, $filePath);
    }


    /**
     * @param $filename
     * @param $content
     * @param $filePath
     * @param string $topic
     * @param string $actionType
     * @return void
     * 日志推送kafka
     */
    public static function logSendKafka($filename, $content, $filePath, string $topic = Kafka::KAFKA_TOPICS['SYNC_MALL_LOG']['topic'], string $actionType = Kafka::KAFKA_TOPICS['SYNC_MALL_LOG']['action_type'])
    {
        $status = CUtil::getConfig('log', 'config', \Yii::$app->id)['status'];
        if ($status === false) {
            // 日志开关关闭不推送
            return;
        }

        $level = 'INFO';
        if (strpos($filename, 'err') !== false || strpos($filename, 'udpLog') !== false) {
            $level = 'ERROR';
        }
        if (strpos($filename, 'warn') !== false) {
            $level = 'WARN';
        }
        // 增加kafka.message中标识
        $contentData             = json_decode($content, true);
        $contentData['project']  = 'dreame-mall-php';
        $contentData['line']     = strval($contentData['line']);
        $contentData['httpCode'] = intval($contentData['httpCode']);
        $contentJson             = json_encode($contentData, 320);
        $logData                 = [
                'project'   => PRO_NAME,
                'level'     => $level,
                'message'   => mb_convert_encoding($contentJson, "UTF-8", ["UTF-8", "GBK", "GB2312", "BIG5"]),
                'file'      => $filePath,
                'timestamp' => time(),
        ];

        $message = [
                'action_type'  => $actionType,
                'msg_id'       => '',
                'trace_id'     => '',
                'balancer_key' => '',
                'data'         => json_encode($logData, 320),
                'timestamp'    => time()
        ];

        // 推送kafka
        Kafka::factory()->send($topic, $message);
    }


    /**
     * @param string $string : 日志内容
     * @param string $filename : 文件名 最大20字符
     * @param string $protocol :发送日志传输协议 0 UDP , 1 TCP
     * 文件日志系统（正式服日志发往日志服务器，开发测试服打印在本地）
     * 说明：日志系统最终生成的日志在【LogServerPath】/Web/年月/项目名/日期/文件名.log
     * 访问方式：例如想查询2021年09月，QQHall项目15号的，文件名为kevin.lin的日志内容,
     * 则日志路径为：[host]/web/source/[账号]/[密码]?file=202109/QQHall/15/kevin.lin
     * [host],[账号],[密码] 请从 WIKI 获取最新的配置
     * http://oa.crosscp.com/dokuwiki/doku.php?id=%E5%85%AD%E8%B1%A1-%E6%97%A5%E5%BF%97%E6%9C%8D%E5%8A%A1
     * 重要说明：请勿将日志服务器当数据库使用，禁止无限死循环打印日志！！！！
     */
    public static function newDebug($string = '', $filename = '', $filePath = '', $protocol = 0)
    {
        // 测试环境不调用sls
        if (YII_ENV_DEV || YII_ENV_LOCAL) {
            self::saveLocalLog($filename, $string);
            return true;
        }
        // 推送kafka
        self::logSendKafka($filename, $string, $filePath);
    }

    /**
     * @param $filename
     * @param $string
     * @return void
     * 本地保存日志文件
     */
    public static function saveLocalLog($filename,$string)
    {
        clearstatcache();
        $string = date("Y-m-d H:i:s") . "|" . strval($string) . "\n";
        $filename = $filename ? $filename : "chart_room";
        $runtime = Yii::getAlias('@runtime');
        $filename = $runtime . '/my_debug/' . date("Ym") . "/" . date("d") . "/" . $filename . ".txt";

        $dirname = dirname($filename);
        if (!is_dir($dirname)) {
            $oldmask = umask(0);
            @mkdir($dirname, 0777, true);
            umask($oldmask);
        }

        $file_exists = file_exists($filename);
        @file_put_contents($filename, $string, FILE_APPEND);
        !$file_exists && @chmod($filename, 0777);
    }

    public static function saveTmpFile($headList = [], $dataList = [], $filename = '', $csv_data = '')
    {
        ini_set("max_execution_time", "3600");
        /** 标题 */
        //输出Excel列名信息
        foreach ($headList as $key => $value) {
            //CSV的Excel支持GBK编码，一定要转换，否则乱码
            $csv_data .= iconv('utf-8', 'gbk', $value) . ',';
        }
        $csv_data = rtrim($csv_data, ',') . "\r\n";

        //计数器
        $num = 0;
        //每隔$limit行，刷新一下输出buffer，不要太大，也不要太小
        $limit = 100000;
        //逐行取出数据，不浪费内存
        $count = count((array)$dataList);
        for ($i = 0; $i < $count; $i++) {
            $num++;
            //刷新一下输出buffer，防止由于数据过多造成问题
            if ($limit == $num) {
                ob_flush();
                flush();
                $num = 0;
            }
            $row = $dataList[$i];
            foreach ($row as $key => $value) {
                $csv_data .= mb_convert_encoding($value,"GBK","UTF-8") . ',';
            }
            $csv_data = rtrim($csv_data, ',') . "\r\n";
        }
        clearstatcache();
        $runtime = Yii::getAlias('@runtime');
        $filename = $runtime . '/my_temp_file/' . date("Ym") . "/" . date("d") . "/" . $filename . ".csv";

        $dirname = dirname($filename);
        if (!is_dir($dirname)) {
            $oldmask = umask(0);
            @mkdir($dirname, 0777, true);
            umask($oldmask);
        }

        $file_exists = file_exists($filename);
        @file_put_contents($filename, $csv_data, FILE_APPEND);
        !$file_exists && @chmod($filename, 0777);
        return [
            'filename' => $filename
        ];
    }

    /**
     * @param array $markdown
     * @param string $chat_id
     * @param bool $extend
     * @param string $api
     * @param bool $http_data 是否需要带上http体数据
     * 向UPD服务器发送Json字符串消息
     *
     * json 字符包含固定格式 {api:xxx,env:0,time:xxx,data:xxx,sign:xxx}
     *
     * api  : string
     * chat_id  : string : 聊天消息群
     * time : int
     * data : [...]string , 自定义消息数组 key从0自增
     * sign : string , 是 api + security_key + time 升序构成的字符串 md5 签名
     * md5 ("api=***&security_key=***&time=152374234")
     *
     * UDP服务器 将会以 markdown消息类型方式 遍历data出数组消息、转发至企业微信群
     *
     * markdown格式：
     * https://work.weixin.qq.com/api/doc#90000/90135/90248/markdown%E6%B6%88%E6%81%AF
     *
     * @return bool
     */
    public static function sendMsgToUdp($markdown = [], $chat_id = '', $extend = true, $api = null, $http_data = true): bool
    {

        $api = is_null($api) ? (YII_ENV_PROD ? "p_1643028439" : "p_1643028386") : $api;
        if ($extend) {
            if (IS_CLI) {
                $hostname = gethostname();
                $config = CUtil::getConfig('hostname', 'common', MAIN_MODULE);
                $serv_ip = $config[$hostname] ?? $hostname;
                $markdown[] = "**消息服务器 :** {$serv_ip}\n";
            } else {
                $proxy_path = CommModel::getProxyPath();
                $markdown[] = "**API :** {$proxy_path}" . "\n";
                $markdown[] = "**客户端IP :** " . self::get_client_ip() . "\n";
                $markdown[] = "**代理服务器 :** " . \Yii::$app->request->userIP . "\n";

                if ($http_data) {
                    $markdown[] = "**POST参数 :** " . json_encode(\Yii::$app->request->post()) . "\n";
                    $markdown[] = "**COOKIE参数 :** " . json_encode($_COOKIE) . "\n";
                }

            }

            $markdown[] = "**时间 :** " . date("Y-m-d H:i:s") . "\n";
        }

        $time = time();
        $data = [
            'api' => $api,
            'chat_id' => $chat_id,
            'time' => $time,
            'data' => $markdown,// map[string]string
        ];

        //UDP 服务器要求签名
        $encode = [
            'api' => $api,
            'chat_id' => $chat_id,
            'time' => $time,
        ];

        $apiKeys = CUtil::getConfig('apiKeys', 'common', MAIN_MODULE);
        $data['sign'] = CommModel::getSign($encode, $apiKeys[$api]);

        return UdpClient::newInstance()->sendData(json_encode($data));
    }


    /**
     * 飞书推送告警信息
     * @param $data
     * @param string $robot
     * @param string $msgType
     * @return bool|string
     */
    public static function sendMsgToFs($data, string $robot = 'orderRefund', string $msgType = 'text')
    {
        $fsUrl = CUtil::getConfig($robot, 'spread', MAIN_MODULE);
        switch ($msgType) {
            case 'text':        // 文本消息
                $body = ['msg_type' => 'text', 'content' => ['text' => is_array($data) ? implode(';', $data) : $data,]];
                break;
            case 'interactive': // 消息卡片
                $elements = [];
                foreach ($data['contents'] ?? [] as $content) {
                    $elements[] = [
                        'tag'  => 'div',
                        'text' => [
                            'tag'     => 'lark_md',
                            'content' => $content
                        ]
                    ];
                }
                $body = [
                    'msg_type' => 'interactive',
                    'card'     => [
                        'header'   =>
                            [
                                'title' => [
                                    "content" => $data['title'] ?? '',
                                    "tag"     => "plain_text"
                                ]
                            ],
                        'elements' => $elements
                    ]
                ];
                break;
            default:
                $body = ['msg_type' => 'text', 'content' => ['text' => is_array($data) ? implode(';', $data) : $data,]];
        }
        return self::curl_post($fsUrl, json_encode($body, 320), ["Content-Type:application/json"]);
    }

    /**
     * @param $string
     * @return string|string[]|null
     * 过滤掉特殊标签
     */
    public static function trimTags($string)
    {

        //过滤掉h5标签
        $string = strip_tags($string);
        //去除空白
        $string = preg_replace('/\s/', '', $string);

        return $string;
    }

    /**
     * @param $data
     * @param string $key
     * @return string
     * 字符串可逆加密
     */
    public static function encrypt($data, $key = "GANG_UP"): string
    {
        $key = md5($key);
        $x = 0;
        $len = strlen($data);
        $l = strlen($key);
        $char = "";
        $str = "";

        for ($i = 0; $i < $len; $i++) {
            if ($x == $l) {
                $x = 0;
            }

            $char .= $key[$x];
            $x++;
        }

        for ($i = 0; $i < $len; $i++) {
            $str .= chr(ord($data[$i]) + (ord($char[$i])) % 256);
        }

        return base64_encode($str);
    }

    /**
     * @param $data
     * @param string $key
     * @return string
     * 字符串可逆解密
     */
    public static function decrypt($data, $key = "GANG_UP"): string
    {
        $key = md5($key);
        $x = 0;
        $data = base64_decode($data);
        $len = strlen($data);
        $l = strlen($key);
        $char = "";
        $str = "";

        for ($i = 0; $i < $len; $i++) {
            if ($x == $l) {
                $x = 0;
            }

            $char .= substr($key, $x, 1);
            $x++;
        }

        for ($i = 0; $i < $len; $i++) {
            if (ord(substr($data, $i, 1)) < ord(substr($char, $i, 1))) {
                $str .= chr((ord(substr($data, $i, 1)) + 256) - ord(substr($char, $i, 1)));
            } else {
                $str .= chr(ord(substr($data, $i, 1)) - ord(substr($char, $i, 1)));
            }
        }

        return $str;
    }

    /**
     * @return string
     * 返回当前函数所有的参数
     */
    public static function getAllParams(): string
    {
        $args = func_get_args();
        if (empty($args)) {
            return "";
        }
        foreach ($args as $key => $arg) {
            if (is_array($arg)) $args[$key] = json_encode($arg);
        }

        return is_array($args) ? implode('|', $args) : "";
    }

    /**
     * @param $num
     * @param $len : 小数点长度
     * @return bool
     * 判断是几位小数
     */
    public static function IsDecimal($num, $len): bool
    {
        return preg_match("/^[0-9]+(.[0-9]{{$len}})$/", $num);
    }

    /**
     * @param $page
     * @param $page_size
     * @return array
     * 分页
     */
    public static function pagination($page, $page_size): array
    {
        $page = CUtil::uint($page, 1);
        $page_size = CUtil::uint($page_size, 1);
        $offset = $page_size * ($page - 1);

        return [$offset, $page_size];
    }

    /**
     * @param $count
     * @param $page_size
     * @return float|int
     * 分页 页码计算
     */
    public static function getPaginationPages($count, $page_size)
    {
        $pages = $page_size > 0 ? ceil($count / $page_size) : 1;
        return CUtil::uint($pages, 1);
    }

    /**
     * @param $date
     * @return array
     * 解析时间控件
     */
    public static function explodeDatePicker($date): array
    {
        $time = explode('~', $date);
        $start = isset($time[0]) ? strtotime(trim($time[0])) : 0;
        $end = isset($time[1]) ? strtotime(trim($time[1])) : 0;
        return [$start, $end];
    }

    /**
     * @param string $date
     * @return array
     * @throws \Exception
     * 根据指定日期获取每周起始和结束日期
     */
    public static function getStartAndEndDayForAWeek($date = ''): array
    {
        $date = $date ? date("Ymd", strtotime($date)) : date("Ymd");
        $date = new \DateTime($date);
        $date->modify('this week');
        $first_day_of_week = $date->format('Ymd');

        $date->modify('this week +6 days');
        $end_day_of_week = $date->format('Ymd');

        return ['start' => $first_day_of_week, 'end' => $end_day_of_week];
    }

    /**
     * @param $string
     * @param string $trim_chars
     * @return string|string[]|null
     * 截取中文字符串
     */
    public static function MbTrim($string, $trim_chars = '\s')
    {
        return preg_replace('/^[' . $trim_chars . ']*(?U)(.*)[' . $trim_chars . ']*$/u', '\\1', $string);
    }


    /**
     * @param array $aData
     * @param string $module
     * @param string $domain
     * @param array $head
     * @return bool|mixed|string
     * API CURL 请求
     */
    public static function appRequest($aData = [], $module = 'basic', $domain = '', $head = [])
    {
        if (!isset($aData['uri'])) {
            return '缺少必要参数';
        }
        $head['version'] = '1.0.0';
        $apiConfig = self::getConfig('api', 'common', $module);
        $head['api'] = isset($apiConfig['apiKeys']) ? trim($apiConfig['apiKeys']) : '';
        $appSecret = isset($apiConfig['apiSecret']) ? trim($apiConfig['apiSecret']) : '';
        $domain = $domain ? $domain : (isset($apiConfig['apiDomain']) ? trim($apiConfig['apiDomain']) : '');
        $file = isset($aData['file']) && $aData['file'] ? trim($aData['file']) : '';
        if ($file) {
            unset($aData['file']);
        }
        $aData['uri'] = $domain . $aData['uri'];
        $aData['request_time'] = time();
        $aData['sign'] = self::getSign(array_merge($aData, $head), $appSecret);
        //文件上传
        if ($file) {
            if (class_exists('CURLFile')) {
                $aData['file'] = new \CURLFile($file);
            } else {
                $aData['file'] = "@{$file}";
            }
        }

        $head = [
            "Cookie:api={$head['api']};version={$head['version']}",
        ];

        $res = self::curl_post($aData['uri'], $aData, $head, 0, true);
        return $res;
    }

    /**
     * @param $arr
     * @param $security_key
     * @return string
     * 签名
     */
    public static function getSign($arr, $security_key): string
    {
        $arr['security_key'] = $security_key;
        //对关联数组按照键名进行升序排序：
        ksort($arr, SORT_STRING); //SORT_STRING - 把每一项作为字符串来处理。
        $target_Arr = [];
        foreach ($arr as $key => $a) {
            if (!is_array($a)) {
                $target_Arr[] = "{$key}={$a}";
            }
        }

        $target_str = implode('&', $target_Arr);

        return md5($target_str);
    }

    /**
     * @return string
     * 获取当前环境的中文名称
     */
    public static function getEnvName()
    {
        $server_name = '正式服';
        if (YII_ENV == YII_ENV_DEV) {
            $server_name = '开发服';
        }
        if (YII_ENV == YII_ENV_TEST) {
            $server_name = '测试服';
        }
        return $server_name;
    }

    /**
     * @param $r_key
     * @param int $second
     * @return bool|int
     * 设置过期时间
     */
    public static function ResetExpire($r_key, $second = 1800)
    {

        $ttl = by::redis('core')->ttl($r_key);

        if ($ttl <= 0) {
            by::redis('core')->expire($r_key, $second);
            $ttl = $second;
        }
        return $ttl;
    }

    /**
     * @param $user_id
     * @return string
     * TOTO 生成订单号
     */
    public static function orderNo($user_id): string
    {
        $pid = getmypid();
        return CUtil::getMillisecond() . $user_id . mt_rand(1000, 9999) . sprintf("%05d", $pid);
    }

    /**
     * @param $arr
     * @return string
     * 数组转XML
     */
    public static function arrayToXml($arr)
    {
        $xml = "<xml>";

        foreach ($arr as $key => $val) {
            if (is_numeric($val)) {
                $xml .= "<" . $key . ">" . $val . "</" . $key . ">";
            } else {
                $xml .= "<" . $key . "><![CDATA[" . $val . "]]></" . $key . ">";
            }
        }

        $xml .= "</xml>";

        return $xml;
    }

    /**
     * @param $xml
     * @return array
     * xml 转数组
     */
    public static function xmlToArray($xml)
    {
        return (array)simplexml_load_string($xml, 'SimpleXMLElement', LIBXML_NOCDATA);
    }


    //直接输出不保存文件
    public static function exportCsv($dataList, $headList, $file_name = '')
    {
        ini_set("max_execution_time", "3600");
        $csv_data = '';
        /** 标题 */
        //输出Excel列名信息
        foreach ($headList as $key => $value) {
            //CSV的Excel支持GBK编码，一定要转换，否则乱码
            $csv_data .= iconv('utf-8', 'gbk', $value) . ',';
        }
        $csv_data = rtrim($csv_data, ',') . "\r\n";

        //计数器
        $num = 0;
        //每隔$limit行，刷新一下输出buffer，不要太大，也不要太小
        $limit = 100000;
        //逐行取出数据，不浪费内存
        $count = count((array)$dataList);
        for ($i = 0; $i < $count; $i++) {
            $num++;
            //刷新一下输出buffer，防止由于数据过多造成问题
            if ($limit == $num) {
                ob_flush();
                flush();
                $num = 0;
            }
            $row = $dataList[$i];
            foreach ($row as $key => $value) {
                $csv_data .= iconv('utf-8', 'gbk', $value) . ',';
            }
            $csv_data = rtrim($csv_data, ',') . "\r\n";
        }

        $file_name = empty($file_name) ? date('YmdHis') : $file_name;
        //解决IE浏览器输出中文名乱码的bug
        if (strpos($_SERVER['HTTP_USER_AGENT'], "MSIE")) {
            $file_name = urlencode($file_name);
            $file_name = str_replace('+', '%20', $file_name);
        }
        $file_name = $file_name . '.csv';
        header("Content-type:text/csv");
        header("Content-Disposition:attachment;filename=" . iconv('utf-8', 'GBK', $file_name));
        header("Expires: 0");
        header("Cache-Control: no-cache");
        header("Pragma: no-cache");
        echo $csv_data;
        exit;
    }

    /**
     * @param array $headList
     * @param callable $fun
     * @param string $file_name
     * @param int $flush_num
     * 分页导出文件
     */
    public static function export_csv_new(array $headList, callable $fun, $file_name = '', $flush_num = 100)
    {
        ini_set("max_execution_time", "3600");
        ob_start();

        $file_name = empty($file_name) ? date('YmdHis') : $file_name;
        //解决IE浏览器输出中文名乱码的bug
//        if (strpos($_SERVER['HTTP_USER_AGENT'], "MSIE")) {
//            $file_name = urlencode($file_name);
//            $file_name = str_replace('+', '%20', $file_name);
//        }
        $file_name .= '.csv';

        header('Content-Encoding: UTF-8');
        header("Content-type:text/csv;charset=UTF-8");
        header("Content-Disposition:attachment;filename=" . $file_name);
        header("Expires: 0");
        header("Cache-Control: no-cache");
        header("Pragma: no-cache");
        header('X-Accel-Buffering: no');

        //打开php标准输出流
        $fp = fopen('php://output', 'a');
        //添加BOM头，以UTF8编码导出CSV文件，如果文件头未添加BOM头，打开会出现乱码。
        fwrite($fp, chr(0xEF) . chr(0xBB) . chr(0xBF));

        //添加导出标题
        fputcsv($fp, $headList);

        $item = call_user_func($fun);

        //计数器
        $num = 0;
        foreach ($item as $arr) {
            array_walk($arr, function ($v) use ($fp) {
                fputcsv($fp, $v);
            });

            $num++;
            //刷新一下输出buffer，防止由于数据过多造成问题
            if ($num % $flush_num == 0) {
                ob_flush();
                flush();
            }

        }

        ob_flush();
        flush();
        fclose($fp);
    }


    /**
     * 多维数组的某列排序
     * @param $array :带排序的数组
     * @param $field :列名
     * @param $desc true-降序，false-升序
     *
     * 如 [['name','age'],...] ,按 age排序
     */
    public static function sortArrayByField(&$array, $field, $desc = FALSE)
    {
        $fieldArr = array_column($array, $field);
        $sort = $desc == FALSE ? SORT_ASC : SORT_DESC;
        array_multisort($fieldArr, $sort, $array);
    }


    /**
     * php 将java map数组转为数组
     * @param $str
     * @return array
     */
    public static function mapToArray($str)
    {
        //截取数据
        $b = mb_strpos($str, '(') + mb_strlen('(');
        $e = mb_strpos($str, ')') - $b;
        $str = trim(mb_substr($str, $b, $e));
        //组成数组
        $queryParts = explode(',', $str);
        $params = array();
        foreach ($queryParts as $param) {
            $item = explode('=', $param);
            $params[trim($item[0])] = trim($item[1]);
        }
        return $params;
    }

    /**
     * 年月日、时分秒 + 3位毫秒数 14位年月日时分秒+3位毫秒数
     * @param string $format
     * @param null $utimestamp
     * @return false|string
     */
    public static function tsTime($format = 'u', $utimestamp = null)
    {
        if (is_null($utimestamp)) {
            $utimestamp = microtime(true);
        }

        $timestamp = floor($utimestamp);
        $milliseconds = round(($utimestamp - $timestamp) * 1000);

        return date(preg_replace('`(?<!\\\\)u`', $milliseconds, $format), $timestamp);
    }


    /**
     * 获取几位随机数
     * @param $num
     * @return false|string
     */
    public static function getRandStr($num)
    {
        $str = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz1234567890';
        $randStr = str_shuffle($str);//打乱字符串
        return substr($randStr, 0, $num);//substr(string,start,length);返回字符串的一部分
    }

    /**
     * 校验并输出用户ID
     * @param $uuid
     * @return int
     */
    public static function checkUuid($uuid)
    {
        // 定义UUID的正则表达式模式
        $pattern = '/[0-9a-f]{8}(-[0-9a-f]{4}){3}-[0-9a-f]{12}/';

        // 使用正则表达式匹配UUID
        if (preg_match($pattern, $uuid)) {
            return $uuid;
        }

        // 如果不匹配，返回0
        return 0;
    }

    /**
     * 批量更新函数
     * @param $data array 待更新的数据，二维数组格式
     * @param array $params array 值相同的条件，键值对应的一维数组
     * @param string $field string 值不同的条件，默认为id
     * @return bool|string
     */
    public static function batchUpdate(array $data, string $field, $tb, array $params = [])
    {
        if (!is_array($data) || !$field || !is_array($params) || empty($tb)) {
            return false;
        }
        $updates = self::parseUpdate($data, $field);

        $where = $params ? self::parseParams($params) : '';

        $fields = array_column($data, $field);
        $fields = implode(',', array_map(function ($value) {
            return "'" . addslashes($value) . "'";
        }, $fields));

        return sprintf("UPDATE %s SET %s WHERE `%s` IN (%s) %s", $tb, $updates, $field, $fields, $where);
    }

    /**
     * 解析update语句
     * @param $data
     * @param $field
     * @return string
     */
    public static function parseUpdate($data, $field): string
    {
        $sql = '';
        $keys = array_keys(current($data));
        foreach ($keys as $column) {
            $sql .= sprintf("`%s` = CASE `%s` \n", $column, $field);
            foreach ($data as $line) {
                $sql .= sprintf("WHEN '%s' THEN '%s' \n", addslashes($line[$field]), addslashes($line[$column]));
            }
            $sql .= "END,";
        }
        return rtrim($sql, ',');
    }

    /**
     * 解析where 条件
     * @param $params
     * @return string
     */
    public static function parseParams($params): string
    {
        $where = [];
        foreach ($params as $key => $value) {
            $where[] = sprintf("`%s` = '%s'", $key, $value);
        }
        return $where ? ' AND ' . implode(' AND ', $where) : '';
    }


    /**
     * 过滤非法参数，防止xss攻击
     * @param $string
     * @return array|string|string[]|null
     */
    public static function removeXss($string)
    {
        // 1.过滤h5字符等
        $string = strip_tags($string);
        // 2.过滤xss攻击（移除逗号的过滤）
        $string = preg_replace('/([\x00-\x08\x0b-\x0c\x0e-\x1f])/', '', $string);
        $search = 'abcdefghijklmnopqrstuvwxyz';
        $search .= 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';
        $search .= '1234567890!@#$%^&*()';
        $search .= '~`";:?+/={}[]-_|\'\\';
        for ($i = 0; $i < strlen($search); $i++) {
            // ;? matches the ;, which is optional
            // 0{0,7} matches any padded zeros, which are optional and go up to 8 chars
            // @ @ search for the hex values
            $string = preg_replace('/(&#[xX]0{0,8}' . dechex(ord($search[$i])) . ';?)/i', $search[$i], $string); // with a ;
            // @ @ 0{0,7} matches '0' zero to seven times
            $string = preg_replace('/(�{0,8}' . ord($search[$i]) . ';?)/', $search[$i], $string); // with a ;
        }
        // now the only remaining whitespace attacks are \t, \n, and \r
        $ra1 = array('javascript', 'vbscript', 'expression', 'applet', 'meta', 'xml', 'blink', 'link', 'style', 'script', 'embed', 'object', 'iframe', 'frame', 'frameset', 'ilayer', 'layer', 'bgsound', 'title', 'base');
        $ra2 = array('onabort', 'onactivate', 'onafterprint', 'onafterupdate', 'onbeforeactivate', 'onbeforecopy', 'onbeforecut', 'onbeforedeactivate', 'onbeforeeditfocus', 'onbeforepaste', 'onbeforeprint', 'onbeforeunload', 'onbeforeupdate', 'onblur', 'onbounce', 'oncellchange', 'onchange', 'onclick', 'oncontextmenu', 'oncontrolselect', 'oncopy', 'oncut', 'ondataavailable', 'ondatasetchanged', 'ondatasetcomplete', 'ondblclick', 'ondeactivate', 'ondrag', 'ondragend', 'ondragenter', 'ondragleave', 'ondragover', 'ondragstart', 'ondrop', 'onerror', 'onerrorupdate', 'onfilterchange', 'onfinish', 'onfocus', 'onfocusin', 'onfocusout', 'onhelp', 'onkeydown', 'onkeypress', 'onkeyup', 'onlayoutcomplete', 'onload', 'onlosecapture', 'onmousedown', 'onmouseenter', 'onmouseleave', 'onmousemove', 'onmouseout', 'onmouseover', 'onmouseup', 'onmousewheel', 'onmove', 'onmoveend', 'onmovestart', 'onpaste', 'onpropertychange', 'onreadystatechange', 'onreset', 'onresize', 'onresizeend', 'onresizestart', 'onrowenter', 'onrowexit', 'onrowsdelete', 'onrowsinserted', 'onscroll', 'onselect', 'onselectionchange', 'onselectstart', 'onstart', 'onstop', 'onsubmit', 'onunload');
        $ra = array_merge($ra1, $ra2);
        $found = true; // keep replacing as long as the previous round replaced something
        while ($found == true) {
            $string_before = $string;
            for ($i = 0; $i < sizeof($ra); $i++) {
                $pattern = '/';
                for ($j = 0; $j < strlen($ra[$i]); $j++) {
                    if ($j > 0) {
                        $pattern .= '(';
                        $pattern .= '(&#[xX]0{0,8}([9ab]);)';
                        $pattern .= '|';
                        $pattern .= '|(�{0,8}([9|10|13]);)';
                        $pattern .= ')*';
                    }
                    $pattern .= $ra[$i][$j];
                }
                $pattern .= '/i';
                $replacement = substr($ra[$i], 0, 2) . '<x>' . substr($ra[$i], 2); // add in <> to nerf the tag
                $string = preg_replace($pattern, $replacement, $string); // filter out the hex tags
                if ($string_before == $string) {
                    // no replacements were made, so exit the loop
                    $found = false;
                }
            }
        }
        // 3. 使用HTML实体编码
        return htmlspecialchars($string, ENT_QUOTES, 'UTF-8');
    }


    /**
     * 将字符串参数变为数组
     * @param $query string
     * @return array
     * */
    public static function decodeUrlQuery($query_str)
    {
        $query_pairs = explode('&', $query_str);

        $params = [];
        foreach ($query_pairs as $query_pair) {
            $item = explode('=', $query_pair);
            $params[$item[0]] = $item[1];
        }
        return $params;
    }

    /**
     * 将参数变为字符串
     * @param $query_array array
     * @return string
     */
    public static function encodeUrlQuery($query_array)
    {
        $tmp = array();
        foreach ($query_array as $key => $value) {
            $tmp[] = $key . '=' . $value;
        }

        return implode('&', $tmp);
    }


    /**
     * 字符串过滤表情包
     * @param $str
     * @return array|string|string[]|null
     */
    public static function filterEmoji($str)
    {
        $str = preg_replace_callback('/./u',
            function (array $match) {
                return strlen($match[0]) >= 4 ? '' : $match[0];
            },
            $str);
        return $str;

    }

    /**
     * @param $time
     * @param bool $ms
     * @return int|mixed
     * 东八区改为零时区
     */
    public static function utc8ToUtc($time, bool $ms = false){
        if(!is_numeric($time) || empty($time)) return $time;
        $time = $time-8*3600;
        if($ms) $time = $time.'000';
        return (int)$time;
    }

    /**
     * @param $time
     * @param bool $ms
     * @return int|mixed
     * 零时区转为东八区
     */
    public static function utcToUtc8($time,bool $ms = false)
    {
        if(!is_numeric($time)|| empty($time)) return $time;
        if ($ms) $time = substr($time, 0, -3);
        $time = $time + 8 * 3600;
        return (int)$time;
    }


    /**
     * 是否为json
     * @param $string
     * @return bool
     */
    public static function isJson($string)
    {
        json_decode($string);
        return (json_last_error() == JSON_ERROR_NONE);
    }



    /**
     * @param $user_id
     * @param $order_no
     * @return false
     * 判断是否使用
     */
    public static function omsLock($user_id,$order_no): bool
    {
        $lockKey = CUtil::getConfig('lockOldErp', 'member', MAIN_MODULE) ?? '';
        $lockTime = CUtil::getConfig('lockOldErpTime', 'member', MAIN_MODULE) ?? 0;
        $testUsers = CUtil::getConfig('testUsers', 'member', MAIN_MODULE) ?? [];
        $order_no = preg_replace("/[a-zA-Z]/", "", $order_no);
        if($testUsers && in_array($user_id,$testUsers)){
            return true;
        }
        $result = false;
        if($lockKey){
            if(strlen($order_no)>11){
                $ymd     = substr($order_no,2,6);//订单抽选时间所属年份
                $time   = date("20{$ymd}");
            }else{
                $time = date('Y').date('m').date('d');
            }
            if($time > $lockTime){
                $result = true;
            }
        }
        return $result;
    }


    /**
     * @param $user_id
     * @return array
     * 支付锁
     */
    public static function payLock($user_id): array
    {
        $lockKey     = (CUtil::getConfig('pay', 'config', \Yii::$app->id)['lockPay']);
        $lockMessage = (CUtil::getConfig('pay', 'config', \Yii::$app->id)['lockPayMessage']);
        $testUsers   = (CUtil::getConfig('testUsers', 'config', \Yii::$app->id));
//        $lockKey = CUtil::getConfig('lockPay', 'member', MAIN_MODULE) ?? false;
//        $lockMessage = CUtil::getConfig('lockPayMessage', 'member', MAIN_MODULE) ?? '';
//        $testUsers = CUtil::getConfig('testUsers', 'member', MAIN_MODULE) ?? [];
        if ($testUsers && in_array($user_id, $testUsers)) {
            return [false, 'OK'];
        }
        return [$lockKey, $lockMessage];
    }


    /**
     * @return bool
     * 企业微信公众号迁移
     */
    public static function wxOaLock($user_id = 0)
    {
        $result   = false;
        $lockTime = CUtil::getConfig('lockOldWxOaTime', 'member', MAIN_MODULE) ?? 0;
        $testUsers = CUtil::getConfig('testUsers', 'member', MAIN_MODULE) ?? [];
        if (time() > strtotime($lockTime)||in_array($user_id,$testUsers)) {
            $result = true;
        }
        return $result;
    }

    /**
     * @param $array
     * @param $key
     * @return array
     * 二维数组根据键值去重
     */
    public static function uniqueMultidimArray($array, $key): array
    {
        if (!$array || !$key){
            return [];
        }

        $temp_array = array();
        $num = 0;
        $key_array = array();

        foreach($array as $val) {
            if (!isset($val[$key])){
                return $array;
            }
            if (!in_array($val[$key], $key_array)) {
                $key_array[$num] = $val[$key];
                $temp_array[$num] = $val;
            }
            $num++;
        }
        return $temp_array;
    }

    /**
     * 读取 csv 文件内容
     * @param string $filePath
     * @return array
     */
    public static function readCsv(string $filePath): array
    {
        $handle = fopen($filePath, 'r');

        // 无法打开
        if (!$handle) {
            return [false, '无法打开文件'];
        }

        // 逐行读取
        $rows = [];
        while (($data = fgetcsv($handle)) !== false) {
            $rows[] = $data;
        }
        fclose($handle);
        return [true, $rows];
    }


    /**
     * @param $string
     * @param $start
     * @param $length
     * @param $re
     * @return mixed|string
     * 过滤字符串的敏感词
     */
    public static function hideWord($string, $start = 0, $length = 0, $re = '*'){
        if (empty($string) || empty($length) || empty($re)) return $string;
        $end = $start + $length;
        $strlen = mb_strlen($string,'UTF-8');
        $str_arr = array();
        for ($i = 0; $i < $strlen; $i++) {
            if ($i >= $start && $i < $end)
                $str_arr[] = $re;
            else
                $str_arr[] = mb_substr($string, $i, 1);
        }
        return implode('', $str_arr);
    }

    /**
     * @param $price
     * @param int $type
     * @return mixed
     * 货币单位转换
     */
    public static function totalFee($price, int $type = 0)
    {
        switch (true) {
            case $type == 0:
                $price = sprintf("%.2f", $price);
                $price = bcmul($price, 100);
                return CUtil::uint($price);
                break;

            default:
                $price = CUtil::uint($price);
                $price = bcdiv($price, 100, 2);
                return sprintf("%.2f", $price);
        }
    }


    /**
     * @param $sn
     * @param $buy_time
     * @return false|int
     */
    public static function getTimeBySn($sn,$buy_time){
        $time = 0;
        if (empty($sn) || empty($buy_time)) {
            return $time;
        }
        //解析获取年月日(新7~9 位)
        $str   = substr($sn, 6, 3);
        $arr   = str_split($str);
        $timeY = self::getSnTimeY($arr[0] ?? '');
        $timeM = self::getSnTimeM($arr[1] ?? '');
        $timeD = self::getSnTimeD($arr[2] ?? '');
        if (!empty($timeY) && !empty($timeM) && !empty($timeD)) {
            $time = $timeY .'-'.$timeM .'-'.$timeD;
        }

        if(!empty($time) && $time != date('Y-m-d',strtotime($time))){
            $time = 0;
        }

        //解析获取年份（旧）
        $otime = 0;
        $ostr = substr($sn, 5,4);
        $oarr = str_split($ostr);
        $otimeY = self::getSnTimeY($oarr[0] ?? '');
        $otimeM = self::getSnTimeM($oarr[1] ?? '');
        $otimeD = ($oarr[2] ?? '').($oarr[3] ?? '0');
        if(intval($otimeD)<1 || intval($otimeD)>31){
            $otimeD = '';
        }
        if (!empty($otimeY) && !empty($otimeM) && !empty($otimeD)) {
            $otime = $otimeY .'-'.$otimeM .'-'.$otimeD;
        }

        if(!empty($otime) && $otime != date('Y-m-d',strtotime($otime))){
            $otime = 0;
        }

        if(empty($time) || empty($otime)){
            $time = !empty($time) ? $time : $otime;
            if($time && CUtil::uint($buy_time) <= strtotime($time)){
                return false;
            }
        }else{
            //比较时间
            $time1 = CUtil::uint($buy_time) - strtotime($time);
            $time2 = CUtil::uint($buy_time) - strtotime($otime);
            $time = ($time1 <= 0 && $time2 <= 0) ? false : (($time1 <= 0) ? $otime : (($time2 > 0) ? (($time1 <= $time2) ? $time : $otime) : $time));
        }

        return $time === false ? false : (empty($time) ? 0 : strtotime($time));
    }




    /**
     * @param $timeY
     * @return string
     * 获取SN年份
     */
    public static function getSnTimeY($timeY): string
    {
        if(CUtil::uint($timeY) == 0 && ($timeY != 0 || $timeY != '0')){
            return '';
        }
        $timeY = CUtil::uint($timeY);
        $nowY  = date('Y');
        $timeY = substr($nowY, 0, 3) . $timeY;
        if (CUtil::uint($timeY) > CUtil::uint($nowY)) {
            $timeY = CUtil::uint($timeY) - 10;
        }
        if (intval($timeY) < 2019) { //所有产品不低于2019年
            $timeY = '';
        }
        return (string)$timeY;
    }


    /**
     * @param $timeM
     * @return string
     * 获取Sn月份
     */
    public static function getSnTimeM($timeM): string
    {
        if (empty($timeM)) return '';
        $timeMStr = (string)$timeM;
        if (strtolower($timeMStr) == 'a') {
            $timeM = 10;
        }
        if (strtolower($timeMStr) == 'b') {
            $timeM = 11;
        }
        if (strtolower($timeMStr) == 'c') {
            $timeM = 12;
        }

        $timeM = CUtil::uint($timeM);
        $len   = mb_strlen($timeM, 'UTF-8');

        if ($len == 1) {
            $timeM = '0' . $timeM;
        } elseif ($len == 2 && $timeM <= 12) {
            $timeM = (string)$timeM;
        } else {
            $timeM = '';
        }
        return $timeM;
    }


    /**
     * @param $timeD
     * @return string
     * 获取Sn日期
     */
    public static function getSnTimeD($timeD): string
    {
        if (empty($timeD)) return '';
        $timeDStr = (string)$timeD;
        $position = 0;
        $ascii    = ord(strtoupper($timeDStr));
        if ($ascii >= ord('A') && $ascii <= ord('H')) {
            $position = $ascii - ord('A') + 1;
        }

        if($ascii >= ord('J') && $ascii <= ord('N') ){
            $position = $ascii - ord('A');
        }

        if($ascii >= ord('P') && $ascii <= ord('X') ){
            $position = $ascii - ord('A') - 1;
        }

        if($ascii == ord('I') || $ascii == ord('O')){
            return '';
        }

        if ($position) {
            $timeD = CUtil::uint($position) + 9;
        } else {
            $timeD = CUtil::uint($timeD);
        }

        $len = mb_strlen($timeD, 'UTF-8');

        if ($len == 1) {
            $timeD = '0' . $timeD;
        } elseif ($len == 2 && $timeD <= 31) {
            $timeD = (string)$timeD;
        } else {
            $timeD = '';
        }

        return $timeD;
    }

    /**
     * 获取URL中的参数
     *
     * @param string $url
     * @return array [url, params]
     */
    public static function getUrlParam(string $url): array
    {
        $params = [];

        // 解析URL获取查询参数
        $query = parse_url($url, PHP_URL_QUERY);
        parse_str($query, $params);

        return [$url, $params];
    }


    /**
     * 时区转换 convertTimezone('Asia/Shanghai', $complexData, ['created_at', 'updated_at'])
     * @param $timezone
     * @param $data
     * @param array $fields
     * @return mixed
     * @throws \Exception
     */
    public static function convertTimezone($timezone, $data, array $fields)
    {
        if (is_array($data)) {
            foreach ($data as $key => &$value) {
                if (is_array($value)) {
                    $value = self::convertTimezone($timezone, $value, $fields);
                } elseif (is_string($key) && in_array($key, $fields) && !empty($value)) {
                    $dateTime = new \DateTime($value, new \DateTimeZone('UTC'));
                    $dateTime->setTimezone(new \DateTimeZone($timezone));
                    $value = $dateTime->format('Y-m-d H:i:s');
                }
            }
        }
        return $data;
    }

    /**
     * 生成sign（DREAME-CMS验签）
     * 不支持$param为多维数组
     * @param $params
     * @param $timestamp
     * @param $signKey
     * @return string
     */
    public static function generateSign($params, $timestamp, $signKey): string
    {
        // 按照key排序
        ksort($params);

        $str = '';
        $targetArr = [];
        foreach ($params as $key => $param) {
            $value = $param;
            // 数组
            if (is_array($param)) {
                $value = implode(',', $param);
            }
            $targetArr[] = sprintf('%s=%s', $key, $value);

            // 字符串
            $str = implode('&', $targetArr) . $timestamp . $signKey;
        }
        return md5($str);
    }
    /**
     *
     * @param string $event 事件类型
     * @param array $request 请求参数
     * @param array $response 返回参数
     * @param array $headers 请求头
     * @param string $url 请求地址
     * @param int $httpCode http状态码
     * @param string $errMsg 错误信息
     * @param array $otherMsg 其他不在传参中的信息
     * @return string
     * 生成日志信息
     */
    public static function setLogMsg(string $event,$request,$response,$headers = [],string $url = '',$errMsg = '',$otherMsg = '',int $httpCode = 200,$is_transition = 1)
    {
        // 获取调用栈
        $backtrace = debug_backtrace();

        // 假设我们只对直接调用者感兴趣
        $filePath = __FILE__;
        $line = __LINE__;
        $function = __FUNCTION__;
        if($is_transition == 2){
            if (isset($backtrace[2]) && !empty($backtrace[2])) {
                // 获取调用者的文件、行号、函数名
                $filePath = $backtrace[2]['file'] ?? 'Unknown file';
                $line = $backtrace[2]['line'] ?? 'Unknown line';
                $function = $backtrace[2]['function'] ?? 'Unknown function';
            }
        }else{
            if (isset($backtrace[1]) && !empty($backtrace[1])) {
                // 获取调用者的文件、行号、函数名
                $filePath = $backtrace[1]['file'] ?? 'Unknown file';
                $line = $backtrace[1]['line'] ?? 'Unknown line';
                $function = $backtrace[1]['function'] ?? 'Unknown function';
            }
        }

        // 其他信息转为json格式
        if(is_array($otherMsg)){
            $otherMsg = json_encode($otherMsg,JSON_UNESCAPED_UNICODE);
        }elseif(is_object($otherMsg)){
            $otherMsg = json_encode($otherMsg,JSON_UNESCAPED_UNICODE);
        }elseif(is_string($otherMsg)){

        }else{
            $otherMsg = '';
        }
        // 日志信息
        $logMsg = [
                'event'      => $event . '_' . (CUtil::getConfig('log', 'config', \Yii::$app->id)['deployment_location']),
                'eventName'  => CodeModel::EVENT_NAME[$event] ?? $event,
                'url'        => $url,
                'errMsg'     => strval($errMsg),
                'otherMsg'   => $otherMsg,
                'request'    => $request,
                'response'   => $response,
                'headers'    => $headers,
                'httpCode'   => $httpCode,
                'filePath'   => $filePath,
                'function'   => $function,
                'line'       => $line,
                'request_id' => $headers['request_id'][0] ?? ($headers['request_id'] ?? ''),
                'created_at' => date('Y-m-d H:i:s')
        ];
        // 测试调用阿里云日志服务
        // AliYunSls::putLog($event, json_encode($logMsg,JSON_UNESCAPED_UNICODE),'INFO', $filePath);
        // return true;
        if($is_transition == 2){
            // 返回json格式的日志信息
            return json_encode($logMsg,JSON_UNESCAPED_UNICODE);
        }else{
            // 直接调用日志推送方法
            $logMsg = json_encode($logMsg,JSON_UNESCAPED_UNICODE);
            self::newDebug($logMsg,$event,$filePath);
        }
    }
    /**
     * 生成唯一id
     *
     * @param string $split
     * @return void
     */
    public static function getUniqueID(string $split = '-')
    {
        $uid  = uniqid("", true);
        $data = PRO_NAME; // .env文件配置内外网系统
        $data .= START_TIME;
        $data .= HOST_NAME;
        $data .= WEB_PATH;
        $uid  = strtoupper(md5($uid . rand(1000, 9999) . md5($data)));
        return $uid;
        return substr($uid, 0, 8) .
            $split .
            substr($uid, 8, 4) .
            $split .
            substr($uid, 12, 4) .
            $split .
            substr($uid, 16, 4) .
            $split .
            substr($uid, 20, 12);
    }

    /**
     * 创建查询条件数组的辅助方法
     * @param string $field     字段名
     * @param string $condition 条件
     * @param mixed  $value     值
     * @return array
     */
    public static function buildCondition(string $field, string $condition, $value): array
    {
        return [
            'field'     => $field,
            'condition' => $condition,
            'value'     => $value
        ];
    }





    public static function getTenantIdByHeaders()
    {
        $fields = ["Tenant-Id", "Tenant-id", "tenantId", "tenantid", "tenant-id"];

        $headers = Yii::$app->request->headers;

        //headers存在$fields中的参数则返回对应值，否则返回空
        foreach ($fields as $field) {
            if ($headers->has($field)) {
                return $headers->get($field);
            }
        }

        return null;
    }

    public static function timeUntilNextSunday() {
        // 获取当前时间戳
        $currentTime = time();

        // 获取今天结束的时间戳
        $secondsUntilNextSunday = strtotime(date('Y-m-d 23:59:59'));


        // 获取当前是星期几，0（星期日）到 6（星期六）
        $currentDayOfWeek = date('w', $currentTime);

        // 计算到下一个星期日的天数差
        // 如果今天是星期日，则差值为7天（即下一个星期日）
        // 否则，差值为 (7 - 当前星期几)
        $daysUntilSunday = ($currentDayOfWeek == 0) ? 0 : (7 - $currentDayOfWeek);

        $thisSundayTimestamp = strtotime("+$daysUntilSunday days", $secondsUntilNextSunday);

        return $thisSundayTimestamp - $currentTime;
    }

    /**
     * 分页返回封装
     */
    public static function Pg($query,$format=null):array
    {
        $page=self::getRequestParam('post','page',1);
        $pageSize=self::getRequestParam('post','page_size',20);

        list($offset, $limit) = self::pagination($page, $pageSize);
        $count = $query->count();

        $list = $query->limit($limit)->offset($offset)->asArray()->all();

        $pages=ceil($count/$pageSize);

        if(is_callable($format)){
            $list=array_map($format,$list);
        }
        return ['list'=>$list,'pages'=>$pages];
    }

    /**
     * 把组中的模型对象转成数组
     */
    public static function mToArray(array $data,$func=null): array
    {
        return array_map(function ($item) use ($func){
            if(is_callable($func)){
                return $func($item->toArray());
            }else{
                return $item->toArray();
            }
        }, $data);
    }

    /**
     * 保存封装
     */
    public static function mSave($model,array $data): array
    {
        if (isset($data['id']) && $data['id']) {
            $model = $model::findOne($data['id']);
            if (!$model) {
                return ['status' => false, 'message' => 'ID不存在','errors'=>[]];
            }
            $data['utime'] = time();
        } else {
            $data['ctime'] = time();
        }

        $model->setAttributes($data, false);

        if ($model->save()) {
            $data['id']=$model->id;
            return ['status' => true, 'message' => '保存成功', 'data' => $data];
        } else {
            return ['status' => false, 'message' => '保存失败', 'errors' => $model->getErrors()];
        }
    }


    public static function mDelete($model, int $id): array
    {
        $model = $model::findOne($id);
        if (!$model) {
            return ['status' => false, 'message' => 'ID不存在', 'errors' => []];
        }

        $model->is_del = 1;
        $model->dtime  = time();

        if ($model->save()) {
            return ['status' => true, 'message' => '删除成功'];
        } else {
            return ['status' => false, 'message' => '删除失败', 'errors' => $model->getErrors()];
        }
    }


    /**
     * 多记录保存
     * 如果有id修改无id新增
     * 检查历史中主id关联的信息删除不在当前data中的数据
     */
    public static function mSaveMultiple($modelClass,array $data,array $where): array
    {
        $result=[];
        $transaction =  by::dbMaster()->beginTransaction();
        $time=time();
        try {
            $existingIds = array_column($data, 'id');
            $existingRecords = $modelClass::find()->where($where)->indexBy('id')->all();
            foreach ($data as $record) {
                $record['utime'] = $time;
                $record=array_merge($record,$where);
                if (isset($record['id']) && $record['id']) {
                    $model = $existingRecords[$record['id']] ?? null;
                    if (!$model) {
                        return ['status' => false, 'message' => 'ID不存在: ' . $record['id'],'errors'=>[]];
                    }
                } else {
                    $model = new $modelClass();
                    $record['ctime'] = time();
                }

                $model->setAttributes($record, false);

                if (!$model->save()) {
                    $transaction->rollBack();
                    return ['status' => false, 'message' => '保存失败', 'errors' => $model->getErrors()];
                }
                $record['id']=$model->id;
                $result[]=$record;
            }

            // 删除不在当前数据中的记录
            $modelClass::deleteAll(['and', ['not in', 'id', $existingIds], ['in', 'id', array_keys($existingRecords)]]);

            $transaction->commit();

            return ['status' => true, 'message' => '保存成功','data'=>$result];
        } catch (\Exception $e) {
            $transaction->rollBack();
            return ['status' => false, 'message' => '保存失败', 'errors' => $e->getMessage()];
        }
    }


    /**
     * 参数验证Form封装
     * @param null $scenarios
     * @return mixed
     */
    public static function VdForm($form,$scenarios=null)
    {

        if ($scenarios) {
            $form->setScenario($scenarios);
        }

        $form->load(\Yii::$app->request->post(), '');
        if (!$form->validate()) {
            $errors = $form->getErrors();
            if (empty($errors)) {
                return $form;
            }

            $msg='';
            $labels = $form->attributeLabels();
            foreach ($errors as $key => $value) {
                if (isset($labels[$key])) {
                    foreach ($value as $k => $v) {
                        $value[$k] = str_replace($key, $labels[$key], $v);
                    }
                }
                $msg.=implode(',',$value).';';
            }
            $msg=trim($msg,';');
            CUtil::json_response(RespStatusCodeConst::ERROR_CODE, $msg);
        }

        return $form;
    }
    /**
     * @param int $code
     * @param string $msg
     * @param array $data
     * @param string $requestId
     * @param bool $exit
     * @return void
     * oms 输出
     */
    public static function oms_response($code = 100000, $msg = '', $data = [], $requestId = '', $exit = true)
    {
        //cli模式无需设置header头
        if (PHP_SAPI != 'cli') {
            header('Content-Type: application/json');
        }
        // todo 防止循环输出
        static $response = true;
        if (!$response) {
            exit(0);
        }
        $success = intval(intval($code) === 100000);
        $jsonArr = [
            'requestId'   => empty($requestId) ? md5(time()) . mt_rand(1, 1000) : $requestId,
            'code'        => $code,
            'success'     => (bool)$success,
            'msg'         => $msg,
            'sub_code'    => ($code == 100000) ? '' : $code,
            'sub_msg'     => ($code == 100000) ? '' : $msg,
            'retry'       => intval(!$success),
            'rsp_content' => empty($data) ? null : $data,
        ];
        echo json_encode($jsonArr, 320);

        $response = false;

        $exit && exit(0);
    }

    /**
     * 通用缓存方法
     * @param string|array $key
     * @param callable $callback 数据回调函数
     * @param int $expire 过期时间(秒)
     * @param array $options 配置选项
     * @return mixed
     * @throws \RedisException
     */
    public static function rememberCache($key, callable $callback, int $expire = 3600, array $options = [])
    {
        // 合并默认配置
        $options = array_merge([
                'lockExpire'   => 10,      // 锁过期时间
                'retryTimes'   => 5,       // 重试次数
                'sleepTime'    => 200000,   // 重试等待时间(200ms)
                'emptyExpire'  => 60,     // 空值缓存时间
                'randomFactor' => 0    // 随机因子
        ], $options);

        try {
            // 获取key是否为hash
            list($isHash, $key, $field)  = self::cacheHash($key);

            $redis = by::redis('core');
            // 1. 尝试获取缓存
            if ($isHash) {
                $value = $redis->hGet($key, $field);
            } else {
                $value = $redis->get($key);
            }

            if ($value !== false) {
                // 返回缓存数据
                return self::isJson($value) ? json_decode($value, true) : $value;
            }

            // 2. 获取分布式锁
            $lockKey = "lock:{$key}";
            $gotLock = (bool) $redis->set($lockKey, 1, ['NX', 'EX' => $options['lockExpire']]);


            if ($gotLock) {
                try {
                    // 获取数据
                    $data = $callback();

                    // 获取数据中的过期时间
                    $expire = $data['set_expire'] ?? $expire;

                    // 处理空值
                    if (empty($data)) {
                        $data   = is_array($data)?[]:'';
                        $expire = min($expire, $options['emptyExpire']);
                    } else {
                        // 添加随机过期时间，防止缓存雪崩
                        $expire += rand(0, (int) ($expire * $options['randomFactor']));
                    }

                    // 序列化并缓存数据
                    $value = is_array($data) ? json_encode($data, JSON_UNESCAPED_UNICODE) : (string) $data;

                    if ($isHash) {
                        $ttl = $redis->ttl($key);
                        $redis->hSet($key, $field, $value);
                        if ($ttl < 0) {
                            $redis->expire($key, $expire);
                        }
                    } else {
                         $redis->set($key, $value, ['EX' => $expire]);
                    }
                    return $data;

                } finally {
                    // 确保释放锁
                    $redis->del($lockKey);
                }
            }

            // 3. 未获取到锁，进行重试
            for ($i = 0; $i < $options['retryTimes']; $i++) {
                usleep($options['sleepTime']);
                if ($isHash) {
                    $value = $redis->hGet($key, $field);
                } else {
                    $value = $redis->get($key);
                }
                if ($value !== false) {
                    return self::isJson($value) ? json_decode($value, true) : $value;
                }
            }

            // 重试失败后降级到直接返回数据
            try {
                return $callback() ?: '';
            } catch (\Throwable $e) {
                self::debug("rememberCache Fallback error: " . $e->getMessage());
                return '';
            }

        } catch (\RedisException $e) {
            $key = is_array($key) ? json_encode($key) : $key;
            // Redis 异常，记录日志并降级
            self::debug("Redis error in rememberCache: {$key} - " . $e->getMessage());
            try {
                return $callback() ?: '';
            } catch (\Throwable $e) {
                self::debug("rememberCache Fallback error: " . $e->getMessage());
                return '';
            }
        } catch (\Throwable $e) {
            // 其他异常，记录日志并降级
            self::debug("Error in rememberCache: {$key} - " . $e->getMessage());
            try {
                return $callback() ?: '';
            } catch (\Throwable $e) {
                self::debug("rememberCache Fallback error: " . $e->getMessage());
                return '';
            }
        }
    }



    /**
     * @throws Exception
     */
    public static function cacheHash($key):array
    {
        if (is_array($key)) {
            if (count($key) == 2) {
                $key_new = $key[0] ?? [];
                $field   = $key[1] ?? [];
            } else {
                throw new Exception('cacheHash key must be array and count is 2');
            }

            if ((is_string($key_new) || is_numeric($key_new)) && (is_string($field) || is_numeric($field))) {
                return [true, $key_new, $field];
            }
        }
        return [false, $key, ''];
    }

    /**
     * 获取缓存时间
     *
     * 活动即将开始：
     * 当当前没有可用的优惠券（因为没有正在进行的活动），但有即将开始的活动时，缓存的 TTL 设置为最近的 start_time 与当前时间的差值。
     * 活动即将结束：
     * 当存在正在进行的活动时，缓存的 TTL 设置为最近的 end_time 与当前时间的差值。
     * 这确保了当活动结束后，缓存会过期，避免用户获取到已过期的优惠券。
     * @param array $activities
     * @return mixed
     */
    public static function calculateCacheTtl(array $activities)
    {
        $nearest_start_time = null;
        $nearest_end_time   = null;
        $now                = time();

        foreach ($activities as $row) {
            $start_time = $row['start_time']??0;
            $end_time   = $row['end_time']??0;

            if ($start_time > $now && ($nearest_start_time === null || $start_time < $nearest_start_time)) {
                $nearest_start_time = $start_time;
            }

            if ($start_time <= $now && $now < $end_time && ($nearest_end_time === null || $end_time < $nearest_end_time)) {
                $nearest_end_time = $end_time;
            }
        }

        $default_ttl = 3600;
        $ttl         = $default_ttl;

        if ($nearest_end_time !== null) {
            $ttl = $nearest_end_time - $now;
        } elseif ($nearest_start_time !== null) {
            $ttl = $nearest_start_time - $now;
        }

        return min(intval($ttl), $default_ttl);
    }

    /**
     * 获取缓存
     * @param string|array $key 缓存键
     * @return mixed
     */
    public static function getCache($key)
    {
        try {
            list($isHash, $key, $field) = self::cacheHash($key);
            $redis = by::redis('core');
            if ($isHash) {
                $value = $redis->hGet($key, $field);
            } else {
                $value = $redis->get($key);
            }

            if ($value === false) {
                return null;
            }

            return self::isJson($value) ? json_decode($value, true) : $value;
        } catch (\Throwable $e) {
            self::debug("getCache error: {$key} - " . $e->getMessage());
            return null;
        }
    }

    /**
     * 设置缓存
     * @param string|array $key 缓存键
     * @param mixed $value 缓存值
     * @param int $expire 过期时间(秒)
     * @return bool
     */
    public static function setCache($key, $value, int $expire = 3600): bool
    {
        try {
            list($isHash, $key, $field) = self::cacheHash($key);
            $redis = by::redis('core');
            $data = is_array($value) ? json_encode($value, JSON_UNESCAPED_UNICODE) : $value;
            if ($isHash) {
                $redis->hSet($key, $field, $value);
                return  $redis->expire($key, $expire);
            }else{
                return $redis->set($key, $data, ['EX' => $expire]);
            }
        } catch (\Throwable $e) {
            self::debug("setCache error: {$key} - " . $e->getMessage());
            return false;
        }
    }

    /**
     * 删除缓存
     * @param string|array $key 缓存键
     * @param bool $fuzzy 是否模糊匹配
     * @return bool
     */
    public static function delCache($key, bool $fuzzy = false): bool
    {
        try {
            $redis = by::redis('core');
            $keys = is_array($key) ? $key : [$key];

            if ($fuzzy) {
                $deleteKeys = array_reduce($keys, function ($carry, $k) use ($redis) {
                    if (empty($k)) {
                        return $carry;
                    }

                    // 如果结尾是| 删除|号 ，如果结尾没有* 添加*号
                    $k = rtrim($k, '|');
                    $k = rtrim($k, '*') . '*';
                    $k = rtrim($k, '|*') . '*';

                    return array_merge($carry, $redis->keys($k));
                }, []);

                return $redis->del($deleteKeys) > 0;
            }

            return $redis->del($keys) > 0;
        } catch (\Throwable $e) {
            self::debug(sprintf("Cache delete error: %s - %s", json_encode($key), $e->getMessage()));
            return false;
        }
    }


    /**
     * 根据返回的数据生成树形结构
     * @return void
     */
    public static function Ret(array $result)
    {
        if (isset($result['status'])) {
            if ($result['status']) {
                if (isset($result['data']))
                    CUtil::json_response(RespStatusCodeConst::SUCCESS_CODE, 'success', $result['data']);
            } else {
                if (isset($result['message']))
                    CUtil::json_response(RespStatusCodeConst::ERROR_CODE, $result['message'], $result['errors'] ?? []);
            }
        }

        CUtil::json_response(RespStatusCodeConst::SUCCESS_CODE, 'success', $result);
    }

    /**
     * 判断是否为整数或字符串整数
     * @param $value
     * @return bool
     */
    public static function isIntegerString($value): bool
    {
        // 如果是整数类型直接返回true
        if (is_int($value)) {
            return true;
        }

        // 如果是字符串类型，则验证是否为纯整数格式
        if (is_string($value)) {
            return preg_match('/^-?\d+$/', $value);
        }

        return false;
    }

    /**
     * 检查多维数组中某个字段值在数组中是否重复出现
     * @param array $arr 数组
     * @param string $field 字段
     * @return bool
     */
    public static function checkMultiRepeat(array $arr, string $field): bool
    {
        // 提取指定字段的值
        $values = array_column($arr, $field);
        // 统计每个值出现的次数
        $counts = array_count_values($values);

        $hasRepeat = false;
        foreach ($counts as $count) {
            if ($count > 1) {
                $hasRepeat = true;
                break;
            }
        }

        return $hasRepeat;
    }



    public static function groupBy($collection, $keyOrCallback): array
    {
        $groups = [];

        foreach ($collection as $item) {
            if (is_callable($keyOrCallback)) {
                // 如果传入的是回调函数，调用回调函数获取分组键
                $groupKey = call_user_func($keyOrCallback, $item);
            } else {
                // 如果传入的是键名，直接从元素中获取该键的值作为分组键
                if (is_array($item) && array_key_exists($keyOrCallback, $item)) {
                    $groupKey = $item[$keyOrCallback];
                } elseif (is_object($item) && property_exists($item, $keyOrCallback)) {
                    $groupKey = $item->$keyOrCallback;
                } else {
                    continue;
                }
            }

            if (!array_key_exists($groupKey, $groups)) {
                $groups[$groupKey] = [];
            }
            $groups[$groupKey][] = $item;
        }

        return $groups;
    }


    /**
     * 时间戳秒/毫秒转换工具
     *
     * @param int|null $timestamp 原始时间戳，单位秒或毫秒，可为空
     * @param string|null $mode 转换模式：
     *                               - 'to_ms'：秒转毫秒
     *                               - 'to_s'：毫秒转秒
     *                               - null：返回当前时间的毫秒时间戳
     *
     * @return int 返回转换后的时间戳，非法参数返回 -1
     */
    public static function convertTimestamp(int $timestamp = null, string $mode = null): int
    {
        // 1. 返回当前毫秒时间戳（默认）
        if (is_null($timestamp) && is_null($mode)) {
            return (int) (microtime(true) * 1000);
        }

        // 2. 秒转毫秒
        if ($mode === 'to_ms' && is_int($timestamp)) {
            return $timestamp * 1000;
        }

        // 3. 毫秒转秒
        if ($mode === 'to_s' && is_int($timestamp)) {
            return (int) floor($timestamp / 1000);
        }

        // 4. 非法参数，返回错误值
        return -1;
    }


    public static function jsonEncode(array $data): string
    {
        return json_encode($data, JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);
    }

    /**
     * 计算邀请用户的抽奖次数
     * @param int $currentInvitedNum 当前邀请人数
     * @param int $defaultInvitedNum 默认邀请人数
     * @param int $defaultDrawTimes 默认抽奖次数
     * @param int $specialInvitedNum 指定邀请第N位用户额外赠送的抽奖次数
     * @param int $extraDrawTimes 满足指定邀请人数后额外赠送的抽奖次数
     * @param int $maxInvitedNum 最多邀请人数
     * @return int
     */
    public static function calcInvitedDrawTimes(int $currentInvitedNum, int $defaultInvitedNum = 1, int $defaultDrawTimes = 1, int $specialInvitedNum = 3, int $extraDrawTimes = 5, int $maxInvitedNum = 12): int
    {
        // 确保用户邀请不超过限制
        $currentInvitedNum = min($currentInvitedNum, $maxInvitedNum);

        // 初始化总抽奖次数
        $totalDrawTimes = 0;

        // 逐个处理用户邀请计数
        for ($i = 1; $i <= $currentInvitedNum; $i++) {
            // 每邀请1位用户赠送1次抽奖机会
            if ($i % $defaultInvitedNum == 0) {
                $totalDrawTimes += $defaultDrawTimes;
            }

            // 额外次数：当每邀请第3位用户（3的倍数），额外赠送5次抽奖机会，即3次基础+额外赠送5次总共8次
            if ($i % $specialInvitedNum == 0) {
                $totalDrawTimes += $extraDrawTimes;
            }
        }

        return $totalDrawTimes;
    }

    /**
     * 判断数据是否为数字
     * @param $data
     * @param bool $canString 是否允许字符串数字
     * @param bool $canFloat 是否允许小数
     * @return bool
     */
    public static function isNumeric($data, bool $canString = true, bool $canFloat = false): bool
    {
        // 只能纯数字，只能整数
        if (! $canString && ! $canFloat) {
            return is_int($data);
        }

        // 只能纯数字，可以小数
        if (! $canString && $canFloat) {
            return is_int($data) || is_float($data);
        }

        // 可以是数字字符串，只能整数
        if ($canString && ! $canFloat) {
            return is_numeric($data) && preg_match('/^\d+$/', $data);
        }

        // 可以是数字字符串，可以小数
        if ($canString && $canFloat) {
            return is_numeric($data) && preg_match('/^\d+(\.\d+)?$/', $data);
        }

        return false;
    }

    /**
     * 验证时间戳格式是否正确
     * @param $timestamp
     * @return bool
     */
    public static function isValidTimeStamp($timestamp): bool
    {
        try {
            if (! is_numeric($timestamp) || (int) $timestamp <= 0 || (int) $timestamp != $timestamp) {
                return false;
            }

            if ($timestamp != strtotime(date('Y-m-d H:i:s', $timestamp))) {
                return false;
            }

            return true;
        } catch (\Throwable $e) {
            return false;
        }
    }


    /**
     * 将扁平数组格式化为树形结构
     * @param array $menus 菜单数据数组
     * @param int $pid 父级ID
     * @param string $idField ID字段名称
     * @param string $pidField 父ID字段名称
     * @param string $childrenField 子菜单字段名称
     * @return array 树形结构数组
     */
    public static function formatTree(array $menus, int $pid = 0, string $idField = 'id', string $pidField = 'p_id', string $childrenField = 'children'): array
    {
        $tree = [];
        foreach($menus as $item) {
            if($item[$pidField] == $pid) {
                $children = self::formatTree($menus, $item[$idField], $idField, $pidField, $childrenField);
                if(! empty($children)) {
                    $item[$childrenField] = $children;
                }
                $tree[] = $item;
            }
        }
        return $tree;
    }

    /**
     * 获取指定类型字典数据
     * @param string $code 字典标识
     * @return array
     * @throws BusinessException
     */
    public static function dictData(string $code = ''): array
    {
        $service = SystemDictDataService::getInstance();
        return $service->getList(['code' => $code]);
    }


    public static function getNewAllParams(): string
    {
        $args = func_get_args();
        if (empty($args)) {
            return "";
        }
        foreach ($args as $key => $arg) {
            if (is_array($arg)) $args[$key] = json_encode($arg);
        }

        return is_array($args) ? implode(':', $args) : "";
    }
}
