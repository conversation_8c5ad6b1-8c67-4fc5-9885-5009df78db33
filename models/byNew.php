<?php


namespace app\models;

use app\modules\back\models\TryOrdersModel;
use app\modules\back\models\TryOrdersRefundModel;
use app\modules\common\models\SnRenovateModel;
use app\modules\goods\models\BoundUserOrderModel;
use app\modules\goods\models\DrawActivityModel;
use app\modules\goods\models\DrawActivityPrizeModel;
use app\modules\goods\models\DrawActivityPrizeRecordModel;
use app\modules\goods\models\DrawActivityTaskModel;
use app\modules\goods\models\DrawActivityTaskRecordModel;
use app\modules\goods\models\DrawExternalCouponModel;
use app\modules\goods\models\DrawPrizeModel;
use app\modules\goods\models\DrawTaskModel;
use app\modules\goods\models\EnvelopeRecordModel;
use app\modules\goods\models\GoodsCategoryModel;
use app\modules\goods\models\GoodsPlatformModel;
use app\modules\goods\models\GroupPurchase\GroupPurchaseActivityModel;
use app\modules\goods\models\GroupPurchase\GroupPurchaseActivityGoodsModel;
use app\modules\goods\models\GroupPurchase\GroupPurchaseModel;
use app\modules\goods\models\GroupPurchase\GroupPurchaseMemberModel;
use app\modules\goods\models\IotProductModel;
use app\modules\goods\models\OfficialIntroModel;
use app\modules\goods\models\OPayModel;
use app\modules\goods\models\OrderPayHistoryModel;
use app\modules\goods\models\ProductMatrixModel;
use app\modules\goods\models\SubsidyActivityGoodsModel;
use app\modules\goods\models\SubsidyActivityModel;
use app\modules\goods\models\TradeInActivityGoodsModel;
use app\modules\goods\models\TradeInActivityModel;
use app\modules\goods\models\TradeInCategoryModel;
use app\modules\goods\models\TradeInGoodsModel;
use app\modules\goods\models\TradeInOrderModel;
use app\modules\log\models\RegisterExceptionModel;
use app\modules\main\models\BannerModel;
use app\modules\main\models\BannerPlatformModel;
use app\modules\main\models\CommonActivityModel;
use app\modules\main\models\PlatformModel;
use app\modules\log\models\RegBlackListModel;
use app\modules\main\models\UserEmployeeModel;
use app\modules\main\models\UserBindModel;
use app\modules\main\models\AcType1Model;
use app\modules\main\models\UserPopupModel;
use app\modules\wares\models\GiftCardBlessModel;
use app\modules\wares\models\GiftCardExpendRecordModel;
use app\modules\wares\models\GiftCardGoodsModel;
use app\modules\wares\models\GiftCardResourcesModel;
use app\modules\wares\models\GiftCardModel;
use app\modules\wares\models\GiftUserCardsModel;
use app\modules\goods\models\UserOrderTryConversionModel;
use app\modules\goods\models\UserOrderTryModel;
use app\modules\log\models\ShareCouponDrawLogModel;
use app\modules\log\models\YearReportModel;
use app\modules\log\models\RegWhiteListModel;
use app\modules\log\models\ZhiMaLogModel;
use app\modules\main\models\UserTryModel;
use app\modules\main\models\UserTryPathModel;
use app\modules\wares\models\ActivityModel;
use app\modules\wares\models\ActivityTypeModel;
use app\modules\wares\models\cocreate\CoCreateMaterialModel;
use app\modules\wares\models\cocreate\CoCreateStatisticModel;
use app\modules\wares\models\cocreate\CoCreateUserModel;
use app\modules\wares\models\GoodsTryBuyingModel;
use app\modules\wares\models\GoodsTryBuyingPriceModel;
use app\modules\wares\models\SurveyRecordModel;
use app\modules\wares\models\ZfbPasswordModel;
use app\modules\asset\models\PointPushModel;
use app\modules\goods\models\GroupPurchase\GroupPurchaseRewardModel;
use app\modules\log\models\SendGiftCardLogModel;
use app\modules\main\models\ProductCareModel;

class byNew extends by {


    public static function DrawActivity($clear_cache = false): DrawActivityModel
    {
        return self::model('DrawActivityModel', 'goods', null, null, $clear_cache);
    }

    public static function DrawActivityPrize($clear_cache = false): DrawActivityPrizeModel
    {
        return self::model('DrawActivityPrizeModel', 'goods', null, null, $clear_cache);
    }

    public static function DrawActivityPrizeRecord($clear_cache = false): DrawActivityPrizeRecordModel
    {
        return self::model('DrawActivityPrizeRecordModel', 'goods', null, null, $clear_cache);
    }

    public static function DrawActivityTask($clear_cache = false): DrawActivityTaskModel
    {
        return self::model('DrawActivityTaskModel', 'goods', null, null, $clear_cache);
    }

    public static function DrawActivityTaskRecord($clear_cache = false): DrawActivityTaskRecordModel
    {
        return self::model('DrawActivityTaskRecordModel', 'goods', null, null, $clear_cache);
    }

    public static function DrawPrize($clear_cache = false): DrawPrizeModel
    {
        return self::model('DrawPrizeModel', 'goods', null, null, $clear_cache);
    }

    public static function DrawTask($clear_cache = false): DrawTaskModel
    {
        return self::model('DrawTaskModel', 'goods', null, null, $clear_cache);
    }

    public static function DrawExternalCoupon($clear_cache = false): DrawExternalCouponModel
    {
        return self::model('DrawExternalCouponModel', 'goods', null, null, $clear_cache);
    }

    //卡表
    public static function GiftCard($clear_cache = false): GiftCardModel
    {
        return self::model('GiftCardModel', 'wares', null, null, $clear_cache);
    }

    //卡商品
    public static function GiftCardGoods($clear_cache = false): GiftCardGoodsModel
    {
        return self::model('GiftCardGoodsModel', 'wares', null, null, $clear_cache);
    }

    //卡资源
    public static function GiftCardResources($clear_cache = false): GiftCardResourcesModel
    {
        return self::model('GiftCardResourcesModel', 'wares', null, null, $clear_cache);
    }

    //卡消费记录
    public static function GiftCardExpendRecord($clear_cache = false): GiftCardExpendRecordModel
    {
        return self::model('GiftCardExpendRecordModel', 'wares', null, null, $clear_cache);
    }

    //用户-卡表
    public static function GiftUserCards($clear_cache = false): GiftUserCardsModel
    {
        return self::model('GiftUserCardsModel', 'wares', null, null, $clear_cache);
    }

    public static function CoCreateUserModel($clear_cache = false): CoCreateUserModel
    {
        return self::model('CoCreateUserModel', 'wares', null, null, $clear_cache,'models\cocreate');
    }

    public static function CoCreateMaterialModel($clear_cache = false): CoCreateMaterialModel
    {
        return self::model('CoCreateMaterialModel', 'wares', null, null, $clear_cache,'models\cocreate');
    }

    public static function CoCreateStatisticModel($clear_cache = false): CoCreateStatisticModel
    {
        return self::model('CoCreateStatisticModel', 'wares', null, null, $clear_cache,'models\cocreate');
    }

    public static function GiftCardsBless($clear_cache = false): GiftCardBlessModel
    {
        return self::model('GiftCardBlessModel', 'wares', null, null, $clear_cache);
    }

    public static function ShareCouponDrawLogModel($clear_cache = false): ShareCouponDrawLogModel
    {
        return self::model('ShareCouponDrawLogModel', 'log', null, null, $clear_cache);
    }

    public static function YearReportModel($clear_cache = false): YearReportModel
    {
        return self::model('YearReportModel', 'log', null, null, $clear_cache);
    }


    public static function GoodsTryBuyingModel($clear_cache = false): GoodsTryBuyingModel
    {
        return self::model('GoodsTryBuyingModel', 'wares', null, null, $clear_cache);
    }

    public static function GoodsTryBuyingPriceModel($clear_cache = false): GoodsTryBuyingPriceModel
    {
        return self::model('GoodsTryBuyingPriceModel', 'wares', null, null, $clear_cache);
    }


    public static function ActivityModel($clear_cache = false): ActivityModel
    {
        return self::model('ActivityModel', 'wares', null, null, $clear_cache);
    }

    public static function ActivityTypeModel($clear_cache = false): ActivityTypeModel
    {
        return self::model('ActivityTypeModel', 'wares', null, null, $clear_cache);
    }

    public static function SurveyRecordModel($clear_cache = false): SurveyRecordModel
    {
        return self::model('SurveyRecordModel', 'wares', null, null, $clear_cache);
    }

    //用户试用订单转化
    public static function UserOrderTry($clear_cache = false): UserOrderTryModel
    {
        return self::model('UserOrderTryModel', 'goods', null, null, $clear_cache);
    }

    //用户试用订单转化
    public static function UserOrderTryConversion($clear_cache = false): UserOrderTryConversionModel
    {
        return self::model('UserOrderTryConversionModel', 'goods', null, null, $clear_cache);
    }

    public static function UserTryModel($clear_cache = false): UserTryModel
    {
        return self::model('UserTryModel', 'main', null, null, $clear_cache);
    }

    public static function TryOrdersModel($clear_cache = false): TryOrdersModel
    {
        return self::model('TryOrdersModel', 'back', null, null, $clear_cache);
    }

    public static function TryOrdersRefundModel($clear_cache = false): TryOrdersRefundModel
    {
        return self::model('TryOrdersRefundModel', 'back', null, null, $clear_cache);
    }

    public static function ZhiMaLogModel($clear_cache = false): ZhiMaLogModel
    {
        return self::model('ZhiMaLogModel', 'log', null, null, $clear_cache);
    }

    public static function ZfbPasswordModel($clear_cache = false): ZfbPasswordModel
    {
        return self::model('ZfbPasswordModel', 'wares', null, null, $clear_cache);
    }

    public static function UserTryPathModel($clear_cache = false): UserTryPathModel
    {
        return self::model('UserTryPathModel', 'main', null, null, $clear_cache);
    }

    public static function TradeInOrderModel($clear_cache = false): TradeInOrderModel
    {
        return self::model('TradeInOrderModel', 'goods', null, null, $clear_cache);
    }

    public static function TradeInActivityModel($clear_cache = false): TradeInActivityModel
    {
        return self::model('TradeInActivityModel', 'goods', null, null, $clear_cache);
    }

    public static function TradeInActivityGoodsModel($clear_cache = false): TradeInActivityGoodsModel
    {
        return self::model('TradeInActivityGoodsModel', 'goods', null, null, $clear_cache);
    }

    public static function TradeInGoodsModel($clear_cache = false): TradeInGoodsModel
    {
        return self::model('TradeInGoodsModel', 'goods', null, null, $clear_cache);
    }

    public static function TradeInCategoryModel($clear_cache = false): TradeInCategoryModel
    {
        return self::model('TradeInCategoryModel', 'goods', null, null, $clear_cache);
    }

    public static function IotProductModel($clear_cache = false): IotProductModel
    {
        return self::model('IotProductModel', 'goods', null, null, $clear_cache);
    }


    public static function RegisterExceptionModel($clear_cache = false): RegisterExceptionModel
    {
        return self::model('RegisterExceptionModel', 'log', null, null, $clear_cache);
    }


    public static function BannerModel($clear_cache = false): BannerModel
    {
        return self::model('BannerModel', 'main', null, null, $clear_cache);
    }


    public static function PlatformModel($clear_cache = false): PlatformModel
    {
        return self::model('PlatformModel', 'main', null, null, $clear_cache);
    }

    public static function BannerPlatformModel($clear_cache = false): BannerPlatformModel
    {
        return self::model('BannerPlatformModel', 'main', null, null, $clear_cache);
    }

    public static function GoodsPlatformModel($clear_cache = false): GoodsPlatformModel
    {
        return self::model('GoodsPlatformModel', 'goods', null, null, $clear_cache);
    }

    public static function PointPushModel($clear_cache = false): PointPushModel
    {
        return self::model('PointPushModel', 'asset', null, null, $clear_cache);
    }

    public static function RegWhiteListModel($clear_cache = false): RegWhiteListModel
    {
        return self::model('RegWhiteListModel', 'log', null, null, $clear_cache);
    }

    // 订单支付流水表
    public static function orderPayHistoryModel($clear_cache = false): OrderPayHistoryModel
    {
        return self::model('OrderPayHistoryModel', 'goods', null, null, $clear_cache);
    }

    public static function RegBlackListModel($clear_cache = false): RegBlackListModel
    {
        return self::model('RegBlackListModel', 'log', null, null, $clear_cache);
    }


    public static function SnRenovateModel($clear_cache = false): SnRenovateModel
    {
        return self::model('SnRenovateModel', 'common', null, null, $clear_cache);
    }

    public static function OPayModel($clear_cache = false): OPayModel
    {
        return self::model('OPayModel', 'goods', null, null, $clear_cache);
    }

    public static function UserEmployeeModel($clear_cache = false): UserEmployeeModel
    {
        return self::model('UserEmployeeModel', 'main', null, null, $clear_cache);
    }
    public static function UserBindModel($clear_cache = false): UserBindModel
    {
        return self::model('UserBindModel', 'main', null, null, $clear_cache);
    }

    public static function AcType1Model($clear_cache = false): AcType1Model
    {
        return self::model('AcType1Model', 'main', null, null, $clear_cache);
    }

    public static function BoundUserOrderModel($clear_cache = false): BoundUserOrderModel
    {
        return self::model('BoundUserOrderModel', 'goods', null, null, $clear_cache);
    }
    public static function ProductCareModel($clear_cache = false): ProductCareModel
    {
        return self::model('ProductCareModel', 'main', null, null, $clear_cache);
    }

    public static function UserPopupModel($clear_cache = false): UserPopupModel
    {
        return self::model('UserPopupModel', 'main', null, null, $clear_cache);
    }

    public static function GroupPurchaseModel($clear_cache = false): GroupPurchaseModel
    {
        return self::model('GroupPurchaseModel', 'goods', null, null, $clear_cache, 'models\GroupPurchase');
    }

    public static function GroupPurchaseActivityGoodsModel($clear_cache = false): GroupPurchaseActivityGoodsModel
    {
        return self::model('GroupPurchaseActivityGoodsModel', 'goods', null, null, $clear_cache,'models\GroupPurchase');
    }

    public static function GroupPurchaseActivityModel($clear_cache = false): GroupPurchaseActivityModel
    {
        return self::model('GroupPurchaseActivityModel', 'goods', null, null, $clear_cache,'models\GroupPurchase');
    }

    public static function GroupPurchaseMemberModel($clear_cache = false): GroupPurchaseMemberModel
    {
        return self::model('GroupPurchaseMemberModel', 'goods', null, null, $clear_cache,'models\GroupPurchase');
    }
    public static function GroupPurchaseRewardModel($clear_cache = false): GroupPurchaseRewardModel
    {
        return self::model('GroupPurchaseRewardModel', 'goods', null, null, $clear_cache,'models\GroupPurchase');
    }

    public static function ProductMatrixModel($clear_cache = false): ProductMatrixModel
    {
        return self::model('ProductMatrixModel', 'goods', null, null, $clear_cache);
    }

    public static function GoodsCategoryModel($clear_cache = false): GoodsCategoryModel
    {
        return self::model('GoodsCategoryModel', 'goods', null, null, $clear_cache);
    }

    public static function OfficialIntroModel($clear_cache = false): OfficialIntroModel
    {
        return self::model('OfficialIntroModel', 'goods', null, null, $clear_cache);
    }


    public static function PaymentActivityModel($clear_cache = false): \app\modules\goods\models\PaymentActivityModel
    {
        return self::model('PaymentActivityModel', 'goods', null, null, $clear_cache);
    }

    public static function PaymentActivityGoodsModel($clear_cache = false): \app\modules\goods\models\PaymentActivityGoodsModel
    {
        return self::model('PaymentActivityGoodsModel', 'goods', null, null, $clear_cache);
    }

    public static function ActivityAtmosphereModel($clear_cache = false): \app\modules\goods\models\ActivityAtmosphere\ActivityAtmosphereModel
    {
        return self::model('ActivityAtmosphereModel', 'goods', null, null, $clear_cache, 'models\ActivityAtmosphere');
    }

    public static function ActivityAtmosphereGoodsModel($clear_cache = false): \app\modules\goods\models\ActivityAtmosphere\ActivityAtmosphereGoodsModel
    {
        return self::model('ActivityAtmosphereGoodsModel', 'goods', null, null, $clear_cache,'models\ActivityAtmosphere');
    }

    public static function CommonActivityModel($clear_cache = false): CommonActivityModel
    {
        return self::model('CommonActivityModel', 'main', null, null, $clear_cache);
    }
    public static function SendGiftCardLogModel($clear_cache = false): SendGiftCardLogModel
    {
        return self::model('SendGiftCardLogModel', 'log', null, null, $clear_cache);
    }

    public static function EnvelopeRecord($clear_cache = false): EnvelopeRecordModel
    {
        return self::model('EnvelopeRecordModel', 'goods', null, null, $clear_cache);
    }

    /**
     * @param bool $clear_cache
     * @return SubsidyActivityGoodsModel
     * 国补活动表
     */
    public static function SubsidyActivityGoodsModel(bool $clear_cache = false): SubsidyActivityGoodsModel
    {
        return self::model('SubsidyActivityGoodsModel', 'goods', null, null, $clear_cache);
    }

    /**
     * @param bool $clear_cache
     * @return SubsidyActivityModel
     * 国补活动商品表
     */
    public static function SubsidyActivityModel(bool $clear_cache = false): SubsidyActivityModel
    {
        return self::model('SubsidyActivityModel', 'goods', null, null, $clear_cache);
    }
}
