ALTER TABLE `db_dreame_goods`.`member_activity`
ADD COLUMN `rule_position` VARCHAR(100) NOT NULL DEFAULT '' COMMENT '活动规则弹窗位置坐标，JSON格式：{"x": "100", "y": "100"}',
ADD COLUMN `activite_image` VARCHAR(255) NOT NULL DEFAULT '' COMMENT '活动主图存储路径或URL',
ADD COLUMN `navigation_font` VARCHAR(255) NOT NULL DEFAULT '' COMMENT '导航模块字体设置，JSON格式：{"font_type": "100", "font_size": "100", "font_color": "#FFFFFF", "alignment_method": "center"}';



ALTER TABLE `db_dreame_goods`.`member_activity_module_relation`
    ADD COLUMN `module_main_image` VARCHAR(255) NOT NULL DEFAULT '' COMMENT '模块主图存储路径或URL';