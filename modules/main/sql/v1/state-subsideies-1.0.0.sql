CREATE TABLE `db_dreame_goods`.`subsidy_activity`
(
    `id`          BIGINT UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '活动ID',
    `name`        VARCHAR(255) NOT NULL COMMENT '活动名称',
    `start_time`  BIGINT       NOT NULL COMMENT '开始时间',
    `end_time`    BIGINT       NOT NULL COMMENT '结束时间',
    `desc`        TEXT COMMENT '活动描述',
    `create_time` BIGINT       NOT NULL COMMENT '创建时间（时间戳，秒）',
    `update_time` BIGINT       NOT NULL DEFAULT 0 COMMENT '更新时间',
    `delete_time` BIGINT                DEFAULT NULL COMMENT '删除时间',
    `is_deleted`  TINYINT      NOT NULL DEFAULT 0 COMMENT '是否删除：0-未删除，1-已删除',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='国补活动主表';


CREATE TABLE `db_dreame_goods`.`subsidy_activity_goods`
(
    `id`            BIGINT UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '关联ID',
    `activity_id`   BIGINT UNSIGNED NOT NULL COMMENT '关联活动ID（关联 subsidy_activity.id）',
    `goods_name`    VARCHAR(255)  NOT NULL COMMENT '商品名称',
    `subsidy_ratio` DECIMAL(5, 2) NOT NULL COMMENT '国补比例（如：10.00%）',
    `create_time`   BIGINT        NOT NULL COMMENT '创建时间',
    PRIMARY KEY (`id`),
    KEY             `idx_activity_id` (`activity_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='国补活动商品关联表';