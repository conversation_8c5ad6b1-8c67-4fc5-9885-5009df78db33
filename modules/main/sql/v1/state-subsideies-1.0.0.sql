CREATE TABLE `db_dreame_goods`.`subsidy_activity`
(
    `id`          BIGINT UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '活动ID',
    `name`        VARCHAR(255) NOT NULL COMMENT '活动名称',
    `start_time`  BIGINT       NOT NULL COMMENT '开始时间',
    `end_time`    BIGINT       NOT NULL COMMENT '结束时间',
    `desc`        TEXT COMMENT '活动描述',
    `create_time` BIGINT       NOT NULL COMMENT '创建时间（时间戳，秒）',
    `update_time` BIGINT       NOT NULL DEFAULT 0 COMMENT '更新时间',
    `delete_time` BIGINT                DEFAULT NULL COMMENT '删除时间',
    `is_deleted`  TINYINT      NOT NULL DEFAULT 0 COMMENT '是否删除：0-未删除，1-已删除',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='国补活动主表';


CREATE TABLE `db_dreame_goods`.`subsidy_activity_goods`
(
    `id`            BIGINT UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '关联ID',
    `activity_id`   BIGINT UNSIGNED NOT NULL COMMENT '关联活动ID（关联 subsidy_activity.id）',
    `gid`           int  NOT NULL COMMENT '商品ID',
    `subsidy_ratio` DECIMAL(5, 2) NOT NULL COMMENT '国补比例（如：10.00%）',
    `create_time`   BIGINT        NOT NULL COMMENT '创建时间',
    PRIMARY KEY (`id`),
    KEY             `idx_activity_id` (`activity_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='国补活动商品关联表';

-- 后台菜单配置
-- 营销推广模块下添加国补活动菜单
INSERT INTO `db_dreame_admin`.`t_uri_copy1` (`name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`)
VALUES ('subsidyActivity', 27, '', '国补活动', UNIX_TIMESTAMP(), 0, 0, 1, 0, '');

-- 获取刚插入的菜单ID作为父级ID
SET @subsidy_menu_id = LAST_INSERT_ID();

-- 国补活动权限配置
INSERT INTO `db_dreame_admin`.`t_uri_copy1` (`name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES
('subsidyActivityList', @subsidy_menu_id, 'back/subsidy-activity/list', '国补活动列表权限', UNIX_TIMESTAMP(), 0, 1, 0, 0, ''),
('subsidyActivityDetail', @subsidy_menu_id, 'back/subsidy-activity/detail', '国补活动详情权限', UNIX_TIMESTAMP(), 0, 1, 0, 0, ''),
('subsidyActivitySave', @subsidy_menu_id, 'back/subsidy-activity/save', '国补活动保存权限', UNIX_TIMESTAMP(), 0, 1, 0, 0, ''),
('subsidyActivityDelete', @subsidy_menu_id, 'back/subsidy-activity/delete', '国补活动删除权限', UNIX_TIMESTAMP(), 0, 1, 0, 0, ''),
('subsidyActivityStatus', @subsidy_menu_id, 'back/subsidy-activity/status', '国补活动状态修改权限', UNIX_TIMESTAMP(), 0, 1, 0, 0, ''),
('subsidyActivityGoodsList', @subsidy_menu_id, 'back/subsidy-activity/goods-list', '获取主机商品列表权限', UNIX_TIMESTAMP(), 0, 1, 0, 0, '');