CREATE TABLE `db_dreame_wares`.`wares_cart_0`
(
    `id`      int(10) unsigned NOT NULL AUTO_INCREMENT,
    `user_id` int(10) unsigned NOT NULL DEFAULT '0',
    `gid`     int(10) unsigned NOT NULL DEFAULT '0' COMMENT '商品id',
    `sid`     int(10) unsigned NOT NULL DEFAULT '0' COMMENT '属性id',
    `num`     int(10) unsigned NOT NULL DEFAULT '0' COMMENT '数量',
    `ctime`   bigint NOT NULL DEFAULT '0' COMMENT '创建时间',
    `utime`   bigint NOT NULL DEFAULT '0' COMMENT '更新时间',
    PRIMARY KEY (`id`) USING BTREE,
    KEY       `idx_user_id` (`user_id`) USING BTREE,
    UNIQUE KEY `unqie_u_g_s` (`user_id`,`gid`,`sid`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4  ROW_FORMAT=DYNAMIC COMMENT='积分商品购物车';

CREATE TABLE `db_dreame_wares`.`wares_cart_1`
(
    `id`      int(10) unsigned NOT NULL AUTO_INCREMENT,
    `user_id` int(10) unsigned NOT NULL DEFAULT '0',
    `gid`     int(10) unsigned NOT NULL DEFAULT '0' COMMENT '商品id',
    `sid`     int(10) unsigned NOT NULL DEFAULT '0' COMMENT '属性id',
    `num`     int(10) unsigned NOT NULL DEFAULT '0' COMMENT '数量',
    `ctime`   bigint NOT NULL DEFAULT '0' COMMENT '创建时间',
    `utime`   bigint NOT NULL DEFAULT '0' COMMENT '更新时间',
    PRIMARY KEY (`id`) USING BTREE,
    KEY       `idx_user_id` (`user_id`) USING BTREE,
    UNIQUE KEY `unqie_u_g_s` (`user_id`,`gid`,`sid`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='积分商品购物车';

CREATE TABLE `db_dreame_wares`.`wares_cart_2`
(
    `id`      int(10) unsigned NOT NULL AUTO_INCREMENT,
    `user_id` int(10) unsigned NOT NULL DEFAULT '0',
    `gid`     int(10) unsigned NOT NULL DEFAULT '0' COMMENT '商品id',
    `sid`     int(10) unsigned NOT NULL DEFAULT '0' COMMENT '属性id',
    `num`     int(10) unsigned NOT NULL DEFAULT '0' COMMENT '数量',
    `ctime`   bigint NOT NULL DEFAULT '0' COMMENT '创建时间',
    `utime`   bigint NOT NULL DEFAULT '0' COMMENT '更新时间',
    PRIMARY KEY (`id`) USING BTREE,
    KEY       `idx_user_id` (`user_id`) USING BTREE,
    UNIQUE KEY `unqie_u_g_s` (`user_id`,`gid`,`sid`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='积分商品购物车';

CREATE TABLE `db_dreame_wares`.`wares_cart_3`
(
    `id`      int(10) unsigned NOT NULL AUTO_INCREMENT,
    `user_id` int(10) unsigned NOT NULL DEFAULT '0',
    `gid`     int(10) unsigned NOT NULL DEFAULT '0' COMMENT '商品id',
    `sid`     int(10) unsigned NOT NULL DEFAULT '0' COMMENT '属性id',
    `num`     int(10) unsigned NOT NULL DEFAULT '0' COMMENT '数量',
    `ctime`   bigint NOT NULL DEFAULT '0' COMMENT '创建时间',
    `utime`   bigint NOT NULL DEFAULT '0' COMMENT '更新时间',
    PRIMARY KEY (`id`) USING BTREE,
    KEY       `idx_user_id` (`user_id`) USING BTREE,
    UNIQUE KEY `unqie_u_g_s` (`user_id`,`gid`,`sid`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='积分商品购物车';

CREATE TABLE `db_dreame_wares`.`wares_cart_4`
(
    `id`      int(10) unsigned NOT NULL AUTO_INCREMENT,
    `user_id` int(10) unsigned NOT NULL DEFAULT '0',
    `gid`     int(10) unsigned NOT NULL DEFAULT '0' COMMENT '商品id',
    `sid`     int(10) unsigned NOT NULL DEFAULT '0' COMMENT '属性id',
    `num`     int(10) unsigned NOT NULL DEFAULT '0' COMMENT '数量',
    `ctime`   bigint NOT NULL DEFAULT '0' COMMENT '创建时间',
    `utime`   bigint NOT NULL DEFAULT '0' COMMENT '更新时间',
    PRIMARY KEY (`id`) USING BTREE,
    KEY       `idx_user_id` (`user_id`) USING BTREE,
    UNIQUE KEY `unqie_u_g_s` (`user_id`,`gid`,`sid`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='积分商品购物车';

CREATE TABLE `db_dreame_wares`.`wares_cart_5`
(
    `id`      int(10) unsigned NOT NULL AUTO_INCREMENT,
    `user_id` int(10) unsigned NOT NULL DEFAULT '0',
    `gid`     int(10) unsigned NOT NULL DEFAULT '0' COMMENT '商品id',
    `sid`     int(10) unsigned NOT NULL DEFAULT '0' COMMENT '属性id',
    `num`     int(10) unsigned NOT NULL DEFAULT '0' COMMENT '数量',
    `ctime`   bigint NOT NULL DEFAULT '0' COMMENT '创建时间',
    `utime`   bigint NOT NULL DEFAULT '0' COMMENT '更新时间',
    PRIMARY KEY (`id`) USING BTREE,
    KEY       `idx_user_id` (`user_id`) USING BTREE,
    UNIQUE KEY `unqie_u_g_s` (`user_id`,`gid`,`sid`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='积分商品购物车';

CREATE TABLE `db_dreame_wares`.`wares_cart_6`
(
    `id`      int(10) unsigned NOT NULL AUTO_INCREMENT,
    `user_id` int(10) unsigned NOT NULL DEFAULT '0',
    `gid`     int(10) unsigned NOT NULL DEFAULT '0' COMMENT '商品id',
    `sid`     int(10) unsigned NOT NULL DEFAULT '0' COMMENT '属性id',
    `num`     int(10) unsigned NOT NULL DEFAULT '0' COMMENT '数量',
    `ctime`   bigint NOT NULL DEFAULT '0' COMMENT '创建时间',
    `utime`   bigint NOT NULL DEFAULT '0' COMMENT '更新时间',
    PRIMARY KEY (`id`) USING BTREE,
    KEY       `idx_user_id` (`user_id`) USING BTREE,
    UNIQUE KEY `unqie_u_g_s` (`user_id`,`gid`,`sid`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='积分商品购物车';

CREATE TABLE `db_dreame_wares`.`wares_cart_7`
(
    `id`      int(10) unsigned NOT NULL AUTO_INCREMENT,
    `user_id` int(10) unsigned NOT NULL DEFAULT '0',
    `gid`     int(10) unsigned NOT NULL DEFAULT '0' COMMENT '商品id',
    `sid`     int(10) unsigned NOT NULL DEFAULT '0' COMMENT '属性id',
    `num`     int(10) unsigned NOT NULL DEFAULT '0' COMMENT '数量',
    `ctime`   bigint NOT NULL DEFAULT '0' COMMENT '创建时间',
    `utime`   bigint NOT NULL DEFAULT '0' COMMENT '更新时间',
    PRIMARY KEY (`id`) USING BTREE,
    KEY       `idx_user_id` (`user_id`) USING BTREE,
    UNIQUE KEY `unqie_u_g_s` (`user_id`,`gid`,`sid`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='积分商品购物车';

CREATE TABLE `db_dreame_wares`.`wares_cart_8`
(
    `id`      int(10) unsigned NOT NULL AUTO_INCREMENT,
    `user_id` int(10) unsigned NOT NULL DEFAULT '0',
    `gid`     int(10) unsigned NOT NULL DEFAULT '0' COMMENT '商品id',
    `sid`     int(10) unsigned NOT NULL DEFAULT '0' COMMENT '属性id',
    `num`     int(10) unsigned NOT NULL DEFAULT '0' COMMENT '数量',
    `ctime`   bigint NOT NULL DEFAULT '0' COMMENT '创建时间',
    `utime`   bigint NOT NULL DEFAULT '0' COMMENT '更新时间',
    PRIMARY KEY (`id`) USING BTREE,
    KEY       `idx_user_id` (`user_id`) USING BTREE,
    UNIQUE KEY `unqie_u_g_s` (`user_id`,`gid`,`sid`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='积分商品购物车';

CREATE TABLE `db_dreame_wares`.`wares_cart_9`
(
    `id`      int(10) unsigned NOT NULL AUTO_INCREMENT,
    `user_id` int(10) unsigned NOT NULL DEFAULT '0',
    `gid`     int(10) unsigned NOT NULL DEFAULT '0' COMMENT '商品id',
    `sid`     int(10) unsigned NOT NULL DEFAULT '0' COMMENT '属性id',
    `num`     int(10) unsigned NOT NULL DEFAULT '0' COMMENT '数量',
    `ctime`   bigint NOT NULL DEFAULT '0' COMMENT '创建时间',
    `utime`   bigint NOT NULL DEFAULT '0' COMMENT '更新时间',
    PRIMARY KEY (`id`) USING BTREE,
    KEY       `idx_user_id` (`user_id`) USING BTREE,
    UNIQUE KEY `unqie_u_g_s` (`user_id`,`gid`,`sid`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='积分商品购物车';