<?php
/**
 * <AUTHOR>
 * @date 16/4/2025 上午 11:09
 */

namespace app\modules\main\services;

use app\models\by;
use app\models\CUtil;
use app\models\MyExceptionModel;
use app\modules\common\Singleton;
use app\modules\goods\models\DrawActivityModel;
use app\modules\main\enums\activity\ActivityConfigEnum;
use app\modules\main\models\MemberActivityModel;
use app\modules\main\models\MemberActivityModuleRelationModel;
use app\modules\main\models\MemberActivityModuleResourceModel;
use yii\db\Exception;

final class MemberActivityService
{
    use Singleton;

    // 配件专区按钮状态
    const PART_MODULE_BUTTON = [
            1 => '进行中',
            2 => '未开始',
            3 => '已售罄',
            4 => '已结束',
    ];

    // 配件专区商品上架类型
    const PART_TYPE = [
            'EVERY_DAY'      => 1, // 每天
            'ACTIVITY_CYCLE' => 2, // 整个活动期间
    ];

    /**
     * 获取活动完整信息（包含基础信息和模块详情）
     *
     * @param int $id 活动ID（必须为正整数）
     * @return array 返回数组格式：
     *               - 成功: [true, ['base_info' => [...], 'modules' => [...]]]
     *               - 失败: [false, '错误信息']
     * @throws MyExceptionModel 当参数验证失败时抛出
     */
    public function getInfo(int $id, int $userId = 0): array
    {
        // 验证输入参数是否合法
        if ($id <= 0) {
            throw new MyExceptionModel("活动ID必须为正整数");
        }

        try {
            // 根据活动ID 获取活动基础信息
            $baseInfo = $this->getActivityBaseInfo($id);
            if (empty($baseInfo)) {
                throw new MyExceptionModel("活动基础信息获取失败");
            }

            // 根据活动ID 获取活动配置模块
            $configModules = $this->getActivityConfigModules($id);

            if (empty($configModules)) {
                throw new MyExceptionModel("活动配置模块获取失败");
            }

            // 根据活动模块ID 获取模块信息
            $moduleDetails = [];
            foreach ($configModules as $module) {
                $moduleId = $module['id'] ?? null;
                if (!$moduleId) {
                    continue; // 跳过无效模块ID
                }
                $moduleInfo = $this->getModuleInfo($module, $userId, $baseInfo);
                if (!empty($moduleInfo)) {
                    $moduleDetails[] = $moduleInfo;
                }
            }

            // 组装活动信息
            // 返回结果
            return [true, ['base_info' => $baseInfo, 'modules' => $moduleDetails]];
        } catch (MyExceptionModel $myExceptionModel) {
            // 捕获异常并返回错误信息
            $error = $myExceptionModel->getMessage();
            CUtil::debug($error, 'err.member_activity_service');
            return [false, '获取活动详情失败！'];
        }
    }

    /**
     * 获取活动基础信息
     *
     * @param int $id 活动ID
     * @return array 活动基础信息数组，找不到返回空数组
     */
    private function getActivityBaseInfo(int $id): array
    {
        // 定义查询字段
        $fields = ['id', 'title', 'description', 'rule', 'cycle_type', 'start_time', 'end_time'];

        // 获取活动信息
        $activityInfo = MemberActivityModel::getInstance()->getInfo($id, $fields);

        // 返回结果（空结果自动返回空数组）
        return $activityInfo ?: [];
    }

    /**
     * 获取活动配置模块信息
     *
     * 1. 首先查询活动关联的模块基础信息
     * 2. 批量获取这些模块对应的资源信息（模块名称和代码）
     * 3. 合并基础信息和资源信息，并解析extra字段
     *
     * @param int $activityId 活动ID
     * @return array 返回处理后的模块配置数组，结构示例：
     * [
     *     [
     *         'id' => 1, // 模块ID
     *         'activity_id' => 1, // 所属活动ID
     *         'module_res_id' => 5, // 模块资源ID
     *         'title' => '抽奖活动', // 模块标题
     *         'summary' => '活动描述', // 模块描述
     *         'start_time' => 1640995200, // 开始时间戳
     *         'end_time' => 1643673600, // 结束时间戳
     *         'extra' => [], // 解析后的额外配置
     *         'module_name' => '抽奖模块', // 模块名称
     *         'module_code' => 'DRAW' // 模块代码
     *     ],
     *     ...
     * ]
     * 如果没有数据返回空数组
     */
    private function getActivityConfigModules(int $activityId): array
    {
        // 1. 查询活动关联的模块基础信息
        // 查询字段包括：ID、活动ID、资源ID、标题、描述、时间信息和额外配置
        $activityConfigModules = MemberActivityModuleRelationModel::getInstance()->getModuleListByActivityId($activityId, ['id', 'activity_id', 'module_res_id', 'title', 'summary', 'start_time', 'end_time', 'extra']);

        // 如果没有查询到数据，直接返回空数组
        if (empty($activityConfigModules)) {
            return [];
        }

        // 2. 批量获取模块资源信息
        // 提取所有模块资源ID
        $moduleResIds = array_column($activityConfigModules, 'module_res_id');
        // 批量查询资源信息（只需要模块名称和代码）
        $moduleInfos = MemberActivityModuleResourceModel::getInstance()
                ->getBatchInfoByModuleIds($moduleResIds, ['id', 'module_name', 'module_code']);

        // 3. 合并模块信息
        return array_map(function ($item) use ($moduleInfos) {
            // 获取当前模块对应的资源信息
            $moduleInfo = $moduleInfos[$item['module_res_id']] ?? [];

            // 解析extra字段（如果是JSON字符串则解码）
            if (isset($item['extra']) && is_string($item['extra'])) {
                $item['extra'] = json_decode($item['extra'], true) ?? [];
            }

            // 合并基础信息和资源信息
            return array_merge($item, [
                    'module_name' => $moduleInfo['module_name'] ?? '', // 模块名称
                    'module_code' => $moduleInfo['module_code'] ?? ''  // 模块代码
            ]);
        }, $activityConfigModules);
    }


    /**
     * 根据模块类型获取对应的处理后的模块信息
     *
     * 该方法作为模块信息处理的路由中心，根据不同的模块类型(module_code)分发到对应的专用处理方法。
     * 每个模块类型有独立的处理方法，确保业务逻辑解耦和可维护性。
     *
     * ### 支持的模块类型及对应处理方法：
     * | 模块类型(MODULE_CODE) | 处理方法                     | 说明                     |
     * |----------------------|----------------------------|------------------------|
     * | DRAW                 | getDrawModuleInfo()        | 抽奖活动模块处理          |
     * | NEW_PERSON           | getNewPersonModuleInfo()   | 新人入会福利模块处理      |
     * | COUPON               | getCouponModuleInfo()      | 优惠券模块处理            |
     * | RECOMMEND_GOODS      | getRecommendGoodsModuleInfo() | 商品推荐模块处理        |
     * | PART_GOODS          | getPartGoodsModuleInfo()   | 配件专区模块处理          |
     * | POINTS_EXCHANGE     | getPointsExchangeModuleInfo() | 积分兑换模块处理        |
     * | CHECKIN             | getCheckinModuleInfo()      | 积分打卡模块处理          |
     * | 其他                | -                          | 返回原始数据不做处理       |
     *
     * ### 典型调用流程：
     * 1. 参数校验 -> 2. 模块路由 -> 3. 专用处理 -> 4. 返回结果
     *
     * @param array $module 模块数据数组，必须包含以下结构：
     *        - module_code: string 模块类型标识（不区分大小写）
     *        - 其他模块特定字段（各模块要求不同）
     * @param int $userId 当前用户ID，部分模块需要用户信息处理业务逻辑
     * @param array $baseInfo 活动基础信息，部分模块需要活动上下文数据
     *
     * @return array 处理后的模块信息，保持原始结构基础上增加处理后的字段
     *
     * @throws MyExceptionModel 在以下情况抛出异常：
     *        - 模块数据缺少module_code字段（错误码：MODULE_CODE_MISSING）
     *        - 子模块处理方法抛出异常时向上传递
     *
     * @example
     * ```
     * $processedModule = $this->getModuleInfo(
     *     ['module_code' => 'DRAW', ...],
     *     12345,
     *     ['start_time' => 1672531200]
     * );
     * ```
     *
     * @see getDrawModuleInfo()        抽奖模块具体实现
     * @see getNewPersonModuleInfo()   新人模块具体实现
     * @see getCouponModuleInfo()      优惠券模块具体实现
     * @see getRecommendGoodsModuleInfo() 商品推荐模块实现
     * @see getPartGoodsModuleInfo()   配件专区模块实现
     * @see getPointsExchangeModuleInfo() 积分兑换模块实现
     * @see getCheckinModuleInfo()     打卡模块实现
     */
    private function getModuleInfo(array $module, int $userId = 0, array $baseInfo = []): array
    {
        // 参数校验 - 确保模块代码存在
        $moduleCode = $module['module_code'] ?? '';
        if (empty($moduleCode)) {
            throw new MyExceptionModel('模块数据无效: 缺少 module_code 字段');
        }

        // 根据模块类型路由到对应的处理方法
        switch (strtoupper($moduleCode)) {
            case 'DRAW': // 抽奖模块处理
                $moduleInfo = $this->getDrawModuleInfo($module);
                break;

            case 'NEW_PERSON': // 新人入会福利模块处理
                $moduleInfo = $this->getNewPersonModuleInfo($module);
                break;

            case 'COUPON': // 优惠券模块处理
                $moduleInfo = $this->getCouponModuleInfo($module,$userId);
                break;

            case 'RECOMMEND_GOODS': // 机器推荐
                $moduleInfo = $this->getRecommendGoodsModuleInfo($module, $userId);
                break;

            case 'PART_GOODS': // 配件专区
                $moduleInfo = $this->getPartGoodsModuleInfo($module, $baseInfo);
                break;
            case 'POINTS_EXCHANGE': // 积分兑换
                $moduleInfo = $this->getPointsExchangeModuleInfo($module, $userId);
                break;
            case 'CHECKIN': // 积分打卡
                $moduleInfo = $this->getCheckinModuleInfo($module, $userId);
                break;
            case 'RED_ENVELOPE': // 现金红包
                $moduleInfo = $this->getRedEnvelopeModuleInfo($module);
                break;
            default: // 其他模块类型不做特殊处理
                $moduleInfo = $module;
                break;
        }

        return $moduleInfo;
    }

    /**
     * 处理抽奖模块信息，过滤不展示给用户的奖品字段
     *
     * 该方法会移除抽奖奖品中以下敏感字段：
     * - prize_num: 奖品库存数量（不展示给用户）
     * - prize_limit: 奖品限制（如每人限领次数）
     * - prize_rate: 中奖概率（百分比）
     * - prize_value: 奖品实际价值（如积分值）
     *
     * @param array $module 包含抽奖模块信息的数组，需包含extra.draw_prize字段
     * @return array 处理后的抽奖模块信息
     *
     * @throws MyExceptionModel 当$module参数缺少必要字段时抛出异常
     */
    private function getDrawModuleInfo(array $module): array
    {
        // 参数校验
        if (!isset($module['extra']['draw_prize']) || !is_array($module['extra']['draw_prize'])) {
            throw new MyExceptionModel('Invalid module data: missing or invalid draw_prize field');
        }

        // 定义需要保留的字段白名单
        $allowedPrizeFields = [
                'prize_id',     // 奖品ID
                'prize_type',   // 奖品类型
                'prize_name',   // 奖品名称
                'prize_image',  // 奖品图片
        ];

        // 处理每个奖品项
        $module['extra']['draw_prize'] = array_map(function ($prizeItem) use ($allowedPrizeFields) {
            // 只保留白名单字段
            return array_intersect_key($prizeItem, array_flip($allowedPrizeFields));
        }, $module['extra']['draw_prize']);

        return $module;
    }


    /**
     * 获取新人入会福利模块的详细信息
     *
     * 1. 查询关联的活动配置信息
     * 2. 将活动信息合并到模块的extra字段中（直接合并到extra根节点）
     * 3. 返回完整的模块信息
     *
     * @param array $module 模块基础信息，必须包含:
     *                     - id: 模块ID
     *                     - module_code: 模块代码
     *                     - extra: 模块额外配置(可选)，结构示例:
     *                       [
     *                           'coupon' => [ // 优惠券列表
     *                               // 优惠券1数据,
     *                               // 优惠券2数据
     *                           ],
     *                           'point' => [ // 积分配置
     *                               'point_value' => 2000,        // 积分值
     *                               'deductible_price' => '20.00' // 积分抵用金额
     *                               'point_multiplier' => 2     // 积分倍数
     *                           ],
     *                           'has_coupon' => 1, // 是否有优惠券
     *                           'has_point' => 1 // 是否有积分
     *                       ]
     * @return array 返回处理后的模块信息，结构:
     *               [
     *                   ...$module原有字段,
     *                   'extra' => [
     *                       ...$module['extra']原有内容,
     *                       ...$activityConfig活动配置内容（直接合并到extra根节点）
     *                   ]
     *               ]
     * @throws MyExceptionModel
     */
    private function getNewPersonModuleInfo(array $module): array
    {
        try {
            // 1. 查询关联的活动配置
            $activityConfig = ActivityConfigService::getInstance()->activityList();

            if (empty($activityConfig)) {
                return $module;
            }

            // 2. 合并活动配置到extra字段
            $extra           = is_array($module['extra'] ?? null) ? $module['extra'] : [];
            $module['extra'] = array_merge($extra, $activityConfig);

            return $module;
        } catch (\Exception $e) {
            $error = $e->getMessage() . "|" . $e->getFile() . ":" . $e->getLine();
            throw new MyExceptionModel($error);
        }

    }

    /**
     * 处理优惠券模块信息，过滤无效优惠券
     *
     * 1. 检查活动ID有效性
     * 2. 获取活动配置详情
     * 3. 转换优惠券数据结构
     * 4. 过滤活动配置中不存在的优惠券
     * 5. 重置数组索引并返回结果
     *
     * @param array $module 模块信息数组，需包含:
     *                     - extra: [
     *                         'coupon_activity_id' => int, // 活动ID
     *                         'coupon' => array // 优惠券列表
     *                       ]
     * @return array 处理后的模块信息，保持原有结构但优惠券列表已过滤
     * @throws MyExceptionModel
     */
    private function getCouponModuleInfo(array $module, $userId): array
    {
        try {
            // 1. 验证活动ID
            $couponActivityId = (int) ($module['extra']['coupon_activity_id'] ?? 0);
            if ($couponActivityId <= 0) {
                $module['extra']['coupon'] = [];
                return $module;
            }

            // 2. 获取活动详情
            list($status, $activityData) = by::activityConfigModel()->getActivityDetail($couponActivityId, $userId);
            if (!$status || empty($activityData['coupon'])) {
                $module['extra']['coupon'] = [];
                return $module;
            }

            // 3. 处理优惠券数据
            if (empty($module['extra']['coupon'])) {
                $module['extra']['coupon'] = [];
                return $module;
            }

            $activityDataCoupon = [];
            foreach ($activityData['coupon'] as $key => $item) {
                $activityDataCoupon[$item['market_id']] = $item;
            }

            // 有效的优惠券ID
            $validCouponIds = array_keys($activityDataCoupon);

            // 4. 过滤无效的优惠券
            foreach ($module['extra']['coupon'] as $key => $value) {
                if (in_array($value['coupon_id'], $validCouponIds)) {
                    $module['extra']['coupon'][$key]['coupon_status'] = $activityDataCoupon[$value['coupon_id']]['coupon_status'];
                } else {
                    unset($module['extra']['coupon'][$key]);
                }
            }
            $module['extra']['coupon'] = array_values($module['extra']['coupon']);

            return $module;
        } catch (\Exception $e) {
            $error = $e->getMessage() . "|" . $e->getFile() . ":" . $e->getLine();
            throw new MyExceptionModel($error);
        }
    }

    /**
     * 获取推荐商品模块的格式化信息（含积分抵扣与分组逻辑）
     *
     * @param array $module 包含推荐商品数据的模块数组
     *        示例结构: ['extra' => [...推荐商品数据], ...其他模块数据]
     * @param int $userId 用户ID（用于计算积分抵扣，游客为0）
     *
     * @return array 格式化后的推荐商品模块数据结构
     *         返回结构示例:
     *         [
     *             [
     *                 'name' => '扫地机',
     *                 'tid'  => '11',
     *                 'arr'  => [商品列表（已移除分类字段）]
     *             ],
     *             ...
     *         ]
     *         如果 $module['extra'] 为空，直接返回原模块
     * @throws MyExceptionModel
     */
    private function getRecommendGoodsModuleInfo(array $module, int $userId = 0): array
    {
        // 若模块中无推荐商品数据，直接返回原模块
        if (empty($module['extra'])) {
            return $module;
        }

        try {
            $recommendGoods = $module['extra'];

            // 初始化积分计算相关变量
            $mPoint    = null;
            $totalCoin = 0;
            $pointRate = 0;

            // 仅当 userId 有效时才执行积分抵扣逻辑
            if ($userId > 0 && strlen((string) $userId) < 11) {
                $mPoint    = by::point();                                     // 积分服务
                $pointRate = by::memberCenterModel()->getPointCrash($userId); // 抵扣比例
                $totalCoin = $mPoint->get($userId);                           // 用户当前总积分
            }

            // 遍历商品，处理每项商品的积分与每期价格
            foreach ($recommendGoods as &$item) {
                $itemPrice = $item['sale_price'];

                if ($mPoint !== null) {
                    $requiredCoin  = $mPoint->convert($itemPrice, 'POINT'); // 商品所需积分
                    $availableCoin = min($totalCoin, $requiredCoin);        // 实际可抵扣积分
                    $finalCoin     = by::Ouser()->__canCoinByBenefit($availableCoin, $itemPrice, $pointRate, $userId);

                    $item['can_point']   = $finalCoin;
                    $item['point_price'] = $mPoint->convert($finalCoin);   // 积分折现
                } else {
                    $item['can_point']   = 0;
                    $item['point_price'] = 0;
                }

                // 计算每期价格（防止除以0）
                $price                     = bcsub($itemPrice, $item['point_price'], 2);
                $item['every_issue_price'] = bccomp($item['free_periods'], '0', 2) === 0
                        ? '0.00'
                        : bcdiv($price, $item['free_periods'], 2);
            }
            unset($item); // 解除引用，避免后续引用污染

            // --------------------
            // 分组处理（按分类）
            // --------------------

            $groupedData = [];  // 分类名 => 商品数组
            $categoryIds = [];  // 分类名 => 分类ID

            foreach ($recommendGoods as $value) {
                $categoryName = $value['category_name'] ?? '未分类';
                $categoryId   = $value['category_id'] ?? 0;

                // 建立分类ID映射（仅记录首次出现）
                if (!isset($categoryIds[$categoryName])) {
                    $categoryIds[$categoryName] = (string) $categoryId;
                }

                // 移除冗余字段
                unset($value['category_id'], $value['category_name']);

                // 加入到对应分类下
                $groupedData[$categoryName][] = $value;
            }

            // 构造返回结构
            $result = [];
            foreach ($groupedData as $categoryName => $goods) {
                $result[] = [
                        'name' => $categoryName,
                        'tid'  => $categoryIds[$categoryName],
                        'arr'  => $goods
                ];
            }

            $module['extra'] = $result;
            return $module;

        } catch (\Exception $e) {
            $error = $e->getMessage() . "|" . $e->getFile() . ":" . $e->getLine();
            throw new MyExceptionModel($error);
        }
    }


    /**
     * 按商品分类对模块数据进行分组处理
     *
     * 处理流程：
     * 1. 检查输入数据有效性
     * 2. 按商品分类进行分组
     * 3. 构建标准化输出结构
     *
     * @param array $module 输入模块数据，需包含：
     *        - 'extra' => array 商品数据列表，每个商品应包含：
     *            - category_name 商品分类名称
     *            - category_id   商品分类ID
     *            - 其他商品属性字段
     *        - 其他模块元数据字段
     * @param array $baseInfo 活动基本信息
     *
     * @return array 处理后的模块数据，结构为：
     *        - 保持原模块所有字段
     *        - 'extra' 字段替换为分组后的结构：
     *          [
     *              [
     *                  'name' => '分类名称',  // string 分类显示名称
     *                  'tid'  => '分类ID',    // string 分类唯一标识
     *                  'arr'  => [            // array 该分类下的商品列表
     *                      // 商品数据（已移除category_id/category_name字段）
     *                  ]
     *              ],
     *              ...
     *          ]
     *        特殊情形：
     *        - 当输入extra为空时，直接返回原模块
     *        - 当商品无分类时，归类到"未分类"（ID=0）
     *
     *
     * 优先级 已结束 > 未开始 > 售罄 > 进行中
     * @throws MyExceptionModel
     */
    private function getPartGoodsModuleInfo(array $module, array $baseInfo): array
    {
        if (empty($module['extra'])) {
            return $module;
        }

        $extra          = $module['extra'];
        $currentTime    = time();
        $currentTimeHMS = date('H:i:s', $currentTime);
        $statusMap      = self::PART_MODULE_BUTTON;
        $activityStart  = (int) ($baseInfo['start_time'] ?? 0);
        $activityEnd    = (int) ($baseInfo['end_time'] ?? 0);
        $stockModel     = by::GoodsStockModel();
        $categories     = [];

        try {
            foreach ($extra as &$goods) {
                $status = 1; // 默认进行中

                // 活动整体时间判断
                if ($currentTime < $activityStart) {
                    $status = 2; // 未开始
                } elseif ($currentTime > $activityEnd) {
                    $status = 4; // 已结束
                } else {
                    // 库存覆盖状态
                    $sku   = $goods['goods_sku'] ?? '';
                    $stock = $stockModel->OptStock($sku);
                    if ($stock < 1) {
                        $status = 3;
                    }

                    $type      = $goods['extra']['type'] ?? 0;
                    $loopStart = $goods['extra']['start_time'] ?? '';
                    $loopEnd   = $goods['extra']['end_time'] ?? '';

                    if ($type === self::PART_TYPE['EVERY_DAY']) {
                        // 每天循环，比较时分秒
                        if ($loopStart > $currentTimeHMS || $loopEnd < $currentTimeHMS) {
                            $status = 2; // 当前不在时间段内
                        }
                    } elseif ($type === self::PART_TYPE['ACTIVITY_CYCLE']) {
                        // 按周期，比较完整时间戳
                        if ((int) $loopStart > $currentTime || (int) $loopEnd < $currentTime) {
                            $status = 2;
                        }
                    }
                }

                // 设置状态
                $goods['status']     = $status;
                $goods['status_tag'] = $statusMap[$status] ?? '';

                // 分类分组
                $catName = $goods['category_name'] ?? '未分类';
                $catId   = (string) ($goods['category_id'] ?? 0);

                if (!isset($categories[$catName])) {
                    $categories[$catName] = [
                            'id'    => $catId,
                            'goods' => []
                    ];
                }

                unset($goods['category_id'], $goods['category_name']);
                $categories[$catName]['goods'][] = $goods;
            }
            unset($goods);

            // 构造结构化输出
            $module['extra'] = array_map(
                    function ($catName) use ($categories) {
                        return [
                                'name' => $catName,
                                'tid'  => $categories[$catName]['id'],
                                'arr'  => $categories[$catName]['goods']
                        ];
                    },
                    array_keys($categories)
            );

            return $module;
        } catch (\Exception $e) {
            $error = $e->getMessage() . "|" . $e->getFile() . ":" . $e->getLine();
            throw new MyExceptionModel($error);
        }
    }

    /**
     * 获取积分兑换模块信息
     *
     * 处理流程：
     * 1. 检查模块数据有效性
     * 2. 验证每个商品的积分兑换规则
     * 3. 设置商品状态和状态标签
     *
     * @param array $module 模块数据，需包含：
     *        - 'extra' => array 商品数据列表，每个商品应包含：
     *            - gid 商品ID
     *            - 其他商品属性字段
     * @param int $userId 用户ID
     *
     * @return array 处理后的模块数据，结构为：
     *        - 保持原模块所有字段
     *        - 'extra' 字段中的每个商品增加：
     *            - status 状态码
     *            - status_tag 状态标签
     *
     */
    private function getPointsExchangeModuleInfo(array $module, int $userId): array
    {
        // 空数据快速返回
        if (empty($module['extra'])) {
            return $module;
        }

        $activityConfigModel = by::activityConfigModel();

        foreach ($module['extra'] as &$goodsItem) {
            $gid = intval($goodsItem['goods_id'] ?? 0);
            // 检查积分兑换规则
            list($status, $statusCode) = $activityConfigModel->checkPointsGoodsRule($userId, $gid);
            $goodsItem['status']     = $statusCode;
            $goodsItem['status_tag'] = ActivityConfigEnum::POINTS_ACTIVITY_RESULT[$statusCode];
        }
        unset($goodsItem);

        return $module;
    }

    /**
     * 获取签到模块信息
     *
     * 处理逻辑：
     * 1. 参数校验
     * 2. 空数据快速返回
     * 3. 测试用户白名单处理（生产环境提前3天）
     * 4. 获取用户签到详情
     *
     * @param array $module 模块数据，需包含extra字段
     * @param int $userId 用户ID
     * @return array 处理后的模块数据
     * @throws MyExceptionModel
     */
    private function getCheckinModuleInfo(array $module, int $userId): array
    {
        // 空数据快速返回
        if (empty($module['extra'])) {
            return $module;
        }

        try {
            $extra = $module['extra'];

            // 测试用户白名单处理
            $testUsers = CUtil::getConfig('testUsers', 'member', MAIN_MODULE) ?: [];
            if ($testUsers && in_array($userId, $testUsers, true)) {
                // 确保start_time存在且为有效数字
                $startTime = $extra['start_time'] ?? null;
                if (is_numeric($startTime)) {
                    $extra['start_time'] = YII_ENV_PROD
                            ? (int) $startTime - 86400 * 3  // 生产环境提前3天
                            : (int) $startTime;            // 其他环境不变
                }
            }

            // 获取签到详情（可能抛出异常）
            $checkInData = ActivityConfigService::getInstance()
                    ->activityCheckDetail($userId, $extra);

            $module['extra'] = array_merge($module['extra'], $checkInData);

            return $module;
        } catch (\Exception $e) {
            $error = $e->getMessage() . "|" . $e->getFile() . ":" . $e->getLine();
            throw new MyExceptionModel($error);
        }
    }

    private function getRedEnvelopeModuleInfo(array $module): array
    {
        return $module;
    }
}