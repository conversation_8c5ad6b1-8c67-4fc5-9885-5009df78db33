<?php

namespace app\modules\main\services;

use app\jobs\CancelOrderJob;
use app\jobs\CancelPayOrderJob;
use app\models\by;
use app\models\byNew;
use app\models\CUtil;
use app\models\MyExceptionModel;
use app\modules\back\services\CouponService;
use app\modules\goods\services\OcfgService;
use app\modules\main\enums\activity\ActivityConfigEnum;
use app\modules\wares\services\goods\IndexGoodsMainService;
use yii\db\Exception;

class WaresOrderService
{
    private static $_instance = NULL;


    protected $memberCenterModel;
    protected $oMainModel;
    protected $goodsMainModel;
    protected $goodsPointsPriceModel;

    public function __construct()
    {
        $this->memberCenterModel     = by::memberCenterModel();
        $this->oMainModel            = by::Omain();
        $this->goodsMainModel        = by::GoodsMainModel();
        $this->goodsPointsPriceModel = by::GoodsPointsPriceModel();
    }

    private function __clone()
    {
    }

    public static function getInstance()
    {
        if (is_null(self::$_instance)) {
            self::$_instance = new self();
        }
        return self::$_instance;
    }

    /**
     * @throws Exception
     */
    public function BuyInfo(int $user_id, array $arr)
    {
        // 针对先试后买订单校验 **ac_id只有先试后买订单才能传**
        if (!empty($arr['try_ac_id'])) {
            $surveyData  = byNew::SurveyRecordModel()->getSurveyRecord(['user_id' => $user_id, 'ac_id' => $arr['try_ac_id']]);
            $auditStatus = $surveyData['audit_status'] ?? 0;
            // 定义审核状态的消息映射
            $auditMessages = [
                    byNew::SurveyRecordModel()::AUDIT_STATUS['WAIT_AUDIT']    => '您已参与过该活动，请耐心等待审核',
                    byNew::SurveyRecordModel()::AUDIT_STATUS['AUDIT_NO_PASS'] => '很遗憾，您未满足本次活动条件'
            ];

            // 如果审核状态存在对应的消息，返回该消息
            if (isset($auditMessages[$auditStatus])) {
                return [false, $auditMessages[$auditStatus]];
            }
        }

        $gcombines = $arr['gcombines'] ?? ""; //商品组合 [{"gid":1,"sid":0,"num":1}]
        $gcombines = (array) json_decode($gcombines, true);
        if (empty($gcombines)) {
            return [false, '商品参数不正确'];
        }
        $aid = $arr['aid'] ?? 0;  //地址id

        //校验参数
        list($s, $data) = $this->__check($user_id, $gcombines, $arr);
        if (!$s) {
            return [false, $data];
        }

        $aData['list'] = $data['aData'] ?? [];
        $tprice        = $data['tprice'] ?? 0;
        $tpoint        = $data['tpoint'] ?? 0;
        $tcoupon       = $data['tcoupon'] ?? 0;

        //积分校验
        $mPoint     = by::point();
        $tcoin      = $mPoint->get($user_id);
        $coin_price = $tpoint > 0 ? $mPoint->convert($tpoint) : 0;

        $price = $tprice;

        //运费权益
        $shipping      = $data['shipping'] ?? 1;
        $shippingPrice = $this->memberCenterModel->getUserRightList($user_id, 'free_shipping');
        $otprice       = CUtil::totalFee($price);
        if ($shipping == 2 || ($shippingPrice && $otprice >= ($shippingPrice * 100))) {
            $fprice = 0;
        } else {
            $fprice = by::model('OfreightModel', 'goods')->GetFreight($user_id, $aid, $otprice, []);
            $fprice = CUtil::totalFee($fprice, 1);
        }

        $real_price     = bcadd($price, $fprice, 2);
        $aData['total'] = [
                'tprice'     => $tprice,
                'fprice'     => $fprice,
                'coin'       => [
                        'tcoin'      => $tcoin,
                        'can_coin'   => $tpoint,
                        'coin_price' => $coin_price,
                        'rate'       => $mPoint::DETUCT_P,
                ],
                'coupon'     => [
                        'coupon_id' => $tcoupon,
                        'card_type' => 0,
                        'cprice'    => 0,
                ],
                'price'      => $price,
                'real_price' => $real_price,
        ];

        $aData['gcombines'] = $gcombines;

        return [true, $aData];
    }


    /**
     * @throws Exception
     * @throws \RedisException
     */
    public function AddRecord($user_id, $api, $arr): array
    {
        //频率限制 5s
        $unique_key = __FUNCTION__;
        list($anti) = $this->oMainModel->ReqAntiConcurrency($user_id, $unique_key, 5, 'EX');
        if (!$anti) {
            return [false, "请勿频繁操作"];
        }

        $ctime     = intval(START_TIME);
        $gcombines = $arr['gcombines'] ?? ""; //商品组合 [{"gid":1,"sid":0,"num":1}]
        if (empty($gcombines)) {
            return [false, "商品组合为空"];
        }

        $referer  = $arr['referer'] ?? '';                    //用户登录链接
        $union    = $arr['union'] ?? '';                      //渠道来源
        $euid     = $arr['euid'] ?? '';                       //渠道来源-标识参数
        $liveMark = CUtil::removeXss($arr['live_mark'] ?? '');//直播标识

        //平台来源
        $platformIds = $arr['platformIds'] ?? [];

        //腾讯来源
        $unionInfo      = by::userAdv()->getLastUnionInfo($user_id);
        $referer        = $unionInfo['referer'] ?? $referer;
        $union          = $unionInfo['union'] ?? $union;
        $euid           = $unionInfo['euid'] ?? $euid;
        $platformSource = $arr['platform_source'] ?? 0; //平台来源
        $source         = $this->oMainModel::UNION_SOURCE[$union] ?? 0;

        //导购、推荐处理
        $guide_id    = by::userGuide()->getGuideByUid($user_id);
        $r_info      = by::userRecommend()->getInfoByUid($user_id);
        $r_id        = $r_info['r_id'] ?? 0;
        $expire_time = $r_info['expire_time'] ?? 0;

        $gcombines = (array) json_decode($gcombines, true);
        // 虚拟商品类型 1:优惠券 2:停车券
        $subtype = $arr['subtype'] ?? 0;

        // 虚拟商品不需要验证地址
        $check_aid = empty($subtype);

        $data = [
                'aid'          => $arr['aid'] ?? 0,
                'note'         => $arr['note'] ?? '',
                'platformIds'  => $arr['platformIds'] ?? [],
                'check_limit'  => true,
                'check_aid'    => $check_aid,
                'check_point'  => true,
                'check_stock'  => true,
                'check_status' => true,
        ];

        list($s, $gbData) = $this->__check($user_id, $gcombines, $data);
        if (!$s || $s === -1) {
            return ($s === -1) ? [-1, $gbData] : [false, $gbData];
        }

        $gcombines = $gbData['gcombines'] ?? [];

        $arr['ctime'] = $ctime;


        $order_no = "";
        $status   = false;

        $payType = $arr['pay_type'] ?? by::Omain()::PAY_BY_NO_SET;//没有设置支付平台
        //支付类型
        $paySource = $arr['pay_source'] ?? by::WxPay()::SOURCE['POINTS'];
        if (!in_array($paySource, by::WxPay()::SOURCE)) {
            return [false, '订单类型不正确！'];
        }


        $db    = by::dbMaster();
        $trans = $db->beginTransaction();

        try {
            //尝试三次创建订单
            for ($i = 0; $i < 3; $i++) {
                $order_no = $this->CreateOrderNo($ctime);
                $input    = [
                        'user_id'        => $user_id,
                        'ctime'          => $ctime,
                        'guide_id'       => $guide_id,
                        'r_id'           => $r_id,
                        'expire_time'    => $expire_time,
                        'source'         => $source,
                        'platformSource' => $platformSource,
                        'type'           => $gbData['order_type'],
                        'order_type'     => $gbData['order_type'],
                ];
                list($status, $order_no) = $this->__safeInsert($db, $order_no, $input);
                if ($status) {
                    break;
                }
            }

            if (!$status) {
                throw new MyExceptionModel($order_no, 2001);
            }

            $otprice     = CUtil::totalFee($gbData['tprice']);     //总价格
            $opoint      = $gbData['tpoint'];                      //总积分价
            $tpointPrice = CUtil::totalFee($gbData['tpointPrice']);//积分金额
            $oprice      = bcadd($otprice, $tpointPrice);          //总原价

            $ocoupon = $gbData['tcoupon']; //总兑换券
            !empty($ocoupon) && $oprice = CUtil::totalFee($gbData['tcouponPrice']);

            $reduce = 0; //总优惠价
            $reduce = bcadd($reduce, $tpointPrice);

            //todo 非运费信息进行预减库存
            $gInfos = $gbData['aData'] ?? [];
            if (empty($gInfos)) {
                throw new MyExceptionModel('商品不存在');
            }

            foreach ($gInfos as $key => $gInfo) {
                $gid = $gInfo['gid'] ?? 0;
                $sid = $gInfo['spec']['sid'] ?? 0;
                $num = $gInfo['num'];
                list($s) = by::GoodsStockModel()->UpdateStock($gid, $sid, $num);
                if (!$s) {
                    throw new MyExceptionModel('库存不够~~', 2001);
                }
            }

            $cprice = 0;
            if ($ocoupon) {
                $cprice         = CUtil::totalFee($gbData['tcouponPrice']);
                $reduce         = bcadd($reduce, $cprice);
                $user_card_info = by::userCard()->getCardById($user_id, $ocoupon);
                $card_type      = $user_card_info['type'] ?? 0;
                if ($card_type != by::userCard()::TYPE['voucher']) {
                    throw new MyExceptionModel('该优惠券不是兑换券！');
                }
            }

            $price = bcsub($oprice, $reduce);
            //运费权益
            $shipping      = $gbData['shipping'] ?? 1;
            $shippingPrice = by::memberCenterModel()->getUserRightList($user_id, 'free_shipping');

            if ($shipping == 2 || ($shippingPrice && $price >= floatval($shippingPrice) * 100)) {
                $fprice = 0;
            } else {
                if (empty($arr['aid'])){
                    throw new MyExceptionModel('请选择收货地址');
                }
                // todo 获取运费
                $fprice = by::model('OfreightModel', 'goods')->GetFreight($user_id, $arr['aid'], $price, []);
            }

            $pay_price = bcadd($price, $fprice);

            //有实际金额，但是实付为O，不允许购买
            if ($otprice && bccomp($pay_price, 0) <= 0) {
                throw new MyExceptionModel('网络繁忙~');
            }

            //导购表数据
            if (!empty($guide_id)) {
                $sourceData = [
                        'user_id' => $user_id,
                        'price'   => $price,
                        'ctime'   => $ctime,
                        'type'    => 1,//内购类型
                ];
                list($s, $m) = by::Osource()->SaveLog($order_no, $guide_id, $sourceData);
                if (!$s) {
                    throw new MyExceptionModel($m);
                }
            }

            //推荐表数据
            if (!empty($r_id) && $r_id != $user_id) {
                $sourceData = [
                        'user_id'     => $user_id,
                        'r_id'        => $r_id,
                        'order_no'    => $order_no,
                        'price'       => $price,
                        'pay_price'   => $pay_price,
                        'expire_time' => $expire_time,
                        'ctime'       => $ctime
                ];

                list($status, $res) = by::osourceR()->SaveLog($sourceData);
                if (!$status) {
                    throw new MyExceptionModel($res);
                }
            }

            //渠道来源表
            if (!empty($union) || !empty($liveMark) || !empty($referer)) {
                $sourceMData = [
                        'user_id'   => $user_id,
                        'union'     => $union,
                        'euid'      => $euid,
                        'order_no'  => $order_no,
                        'price'     => $price,
                        'live_mark' => trim($liveMark),
                        'referer'   => $referer,
                        'ctime'     => $ctime
                ];
                list($status, $res) = by::osourceM()->SaveLog($sourceMData);
                if (!$status) {
                    throw new MyExceptionModel($res);
                }
            }

            //订单表数据
            $ouserData = [
                    'oprice'     => $oprice,
                    'price'      => $price,
                    'fprice'     => $fprice,
                    'coupon_id'  => intval($ocoupon),
                    'coupon_market_id' => $user_card_info['market_id'] ?? 0,
                    'cprice'     => $cprice ?? 0,
                    'coin'       => intval($opoint),
                    'coin_price' => $tpointPrice ?? 0,
                    'coin_logid' => 0,
                    'note'       => $arr['note'] ?? '',
                    'ctime'      => $ctime,
            ];
            by::Ouser()->SaveLog($user_id, $order_no, $ouserData);

            //地址表数据 , 虚拟商品不需要保存地址
            if ($subtype == 0) {
                by::Oad()->SaveLog($user_id, $order_no, $arr['aid'] ?? 0);
            }


            //订单商品表数据
            $oGoodsData = [
                    'coupon_id'  => intval($ocoupon),
                    'cprice'     => $cprice ?? 0,
                    'coin'       => intval($opoint),
                    'coin_price' => $tpointPrice ?? 0,
                    'tprice'     => $oprice,
                    'goods_type' => by::Ogoods()::GOODS_TYPE['WARES']
            ];
            list($s, $m) = by::Ogoods()->SaveWaresLog($user_id, $order_no, $gcombines, $oGoodsData);
            if (!$s) {
                throw new MyExceptionModel($m);
            }


            //订单商品快照数据
            $cfgData = [
                    'ctime'       => $ctime,
                    'gcombines'   => $gcombines,
                    'platformIds' => $platformIds
            ];
            (new OcfgService())->SaveWaresLog($user_id, $order_no, $cfgData);

            //先试后买数据
            if ($paySource == by::WxPay()::SOURCE['BUY_AFTER_PAY']) {
                $userOrderTryModel = byNew::UserOrderTry();
                // 获取用户手机号加密字符串
                $phone = by::Phone()->GetPhoneByUid($user_id);
                if (empty($phone)) {
                    throw new MyExceptionModel('请先授权手机号');
                }
                $phoneStr = $userOrderTryModel->PhoneEncrypt($phone);

                //检查用户是否已有先试后买订单
                $bool = $userOrderTryModel->CheckUserHasTry($user_id, $arr['ac_id'] ?? 0, $phoneStr);
                if ($bool) {
                    throw new MyExceptionModel('您已有该活动先试后买订单，请勿重复下单！');
                }

                $label     = implode(',', array_column($gInfos, 'label'));
                $goodsName = implode(',', array_column($gInfos, 'name'));
                $realPrice = array_sum(array_column($gInfos, 'price'));
                //1.创建订单数据
                $data = [
                        'order_no'   => $order_no,
                        'user_id'    => $user_id,
                        'ac_id'      => $arr['ac_id'] ?? 0,
                        'amount'     => CUtil::uint($realPrice * 100),
                        'label'      => CUtil::uint($label),
                        'phone_str'  => $phoneStr,
                        'goods_name' => $goodsName,
                        'ctime'      => time(),
                        'utime'      => time(),
                ];
                $res  = $userOrderTryModel->SaveLog($data);
                if (!$res[0]) {
                    throw new MyExceptionModel($res[1]);
                }

                //2.保存先试后买用户数据
                $userResult = ZhimaService::getInstance()->SaveTryUser($user_id);
                if (!$userResult[0]) {
                    throw new MyExceptionModel($userResult[1]);
                }
            }


            //todo 如果兑换券免邮，实付款为0，则无需走微信下单
            if (bccomp($pay_price, 0) == 0) {
                $payType = $this->oMainModel::PAY_BY_WX; //无需支付直接算作小程序订单
                $ret     = ['need_pay' => '0', 'order_no' => $order_no];
            } else {
                $ret = ['need_pay' => '1', 'order_no' => $order_no];
            }

            //保存拉起付款参数，重新支付用
            $payData = [
                    'prepay_id' => $ret['prepay_id'] ?? '', 'price' => $pay_price, 'pay_type' => $payType, 'h5_url' => $ret['h5_url'] ?? ''];
            by::model('OPayModel', 'goods')->SaveLog($order_no, $payData);

            $trans->commit();

            if (bccomp($pay_price, 0) == 0) {
                !YII_ENV_PROD && CUtil::debug($paySource . '|' . $order_no, 'base-pay');
                list($s, $msg) = by::BasePayModel()->afterPay($user_id, $order_no, [], [], $paySource, $payType);
                if (!$s) {
                    CUtil::debug($user_id . '|' . $order_no . '|msg:' . $msg, 'err.not_pay.order');
                }
            }

            //todo 清理订单表缓存
            //清理订单列表缓存
            by::Ouser()->DelListCache($user_id);
            by::Ouser()->DelPointCache($user_id);

            //防止有刷缓存问题
            by::Ouser()->DelInfoCache($user_id, $order_no);

            //todo 清理订单地址表缓存
            by::Oad()->DelCache($order_no);

            //todo 清理订单商品不表缓存
            by::Ogoods()->DelListCache($order_no);
            by::Ogoods()->DelList1Cache($user_id);

            //todo 清理订单商品快照表缓存
            by::Ocfg()->DelCache($order_no);

            //todo 清理导购订单表缓存
            !empty($guide_id) && by::Osource()->DelCache($guide_id);

            //todo 清理渠道来源订单表缓存
            !empty($union) && by::osourceM()->delListCache($union);

            //todo 清理订单主表信息
            $this->oMainModel->delInfoCache($user_id, $order_no);

            $this->oMainModel->ReqAntiConcurrency(0, $unique_key, 0, 'DEL');

            $r_price     = CUtil::totalFee($price, 1);
            $ret['coin'] = bcmul($r_price, by::point()::AWARD_P, 2);

            //TODO 绑定用户等级
            $ret['coin'] = by::memberCenterModel()->GetCoinByOrderCoin($user_id, $ret['coin'], time());

            //todo 推送广告
            by::userAdv()->pushAdv($user_id, $order_no, 'COMPLETE_ORDER');

            // 取消支付单（异步处理，不影响主流程），订单关闭前5秒
            \Yii::$app->queue->delay($this->oMainModel::PAY_EXPIRE - 5)->push(new CancelPayOrderJob([
                    'order_no' => $order_no
            ]));

            // 延时队列，异步取消订单
            \Yii::$app->queue->delay($this->oMainModel::PAY_EXPIRE)->push(new CancelOrderJob([
                    'user_id'  => $user_id,
                    'order_no' => $order_no,
            ]));

            return [true, $ret];

        } catch (MyExceptionModel $e) {
            $trans->rollBack();

            $this->oMainModel->__addRecord($e, $gcombines, $unique_key, $user_id, $paySource);

            return [false, $e->getMessage()];

        } catch (\Exception $e) {

            $trans->rollBack();

            $this->oMainModel->__addRecord($e, $gcombines, $unique_key, $user_id, $paySource);

            return [false, '网络繁忙,请稍候'];
        }
    }


    public function __safeInsert($db = null, $order_no = '', $input = []): array
    {
        $user_id        = CUtil::uint($input['user_id'] ?? 0);
        $guide_id       = CUtil::uint($input['guide_id'] ?? 0);
        $r_id           = CUtil::uint($input['r_id'] ?? 0);
        $expire_time    = CUtil::uint($input['expire_time'] ?? 0);
        $source         = CUtil::uint($input['source'] ?? 0);
        $platformSource = CUtil::uint($input['platformSource'] ?? 0);
        $type           = CUtil::uint($input['type'] ?? 0);
        $ctime          = CUtil::uint($input['ctime'] ?? intval(START_TIME));


        try {
            $db = is_null($db) ? by::dbMaster() : $db;
            $tb = $this->oMainModel::tbName($ctime);

            $save = ['order_no' => $order_no, 'user_id' => $user_id, 'ctime' => $ctime, 'type' => $type];

            $save['source'] = $source;

            //平台来源
            $save['platform_source'] = $platformSource;

            if (empty($save['source'])) {
                !empty($guide_id) && $save['source'] = 1;

                if (!empty($r_id) && $r_id != $user_id && !empty($expire_time) && $ctime <= $expire_time) {
                    if (empty($guide_id)) {
                        $save['source'] = 2;
                    } else {
                        $save['source'] = 3;
                    }
                }
            }

            $db->createCommand()->insert($tb, $save)->execute();

            return [true, $order_no];

        } catch (\Exception $e) {

            return [false, $e->getMessage()];
        }
    }


    public function CreateOrderNo($time): string
    {
        $ym   = date('ym', intval($time));
        $date = date("Ymd");
        $unix = sprintf("%.3f", microtime(true));
        list($now, $mil) = explode('.', $unix);

        $config = CUtil::getConfig('hostid', 'common', MAIN_MODULE);
        $hostid = $config[HOST_NAME] ?? mt_rand(0, 99);
        $hostid = sprintf("%02d", $hostid);//有些项目服务器数量可能两位数
        $rand   = mt_rand(300, 399);       //防止时间回拨，减少碰撞概率 积分商品

        $second = $now - strtotime('today');
        $second = sprintf("%05d", $second);
        $pid    = sprintf("%05d", getmypid());
        $oid    = "{$date}{$second}{$pid}{$hostid}{$rand}{$ym}{$mil}";
        usleep(1000);//保证1ms一个
        return $oid;
    }


    /**
     * @param $user_id
     * @param $gcombines
     * @param array $data
     * @return array
     * @throws \yii\db\Exception
     * 校验订单
     */
    private function __check($user_id, $gcombines, array $data): array
    {
        if (empty($gcombines) || $user_id <= 0 || strlen($user_id) > 11) {
            return [false, '非法参数'];
        }

        //是否校验购买限制
        $checkLimit  = $data['check_limit'] ?? false;
        $checkAid    = $data['check_aid'] ?? false;
        $checkPoint  = $data['check_point'] ?? false;
        $checkStock  = $data['check_stock'] ?? false;
        $checkStatus = $data['check_status'] ?? false;

        //todo 收货地址校验
        $aid = $data['aid'] ?? 0;
        if ($checkAid) {
            if ($aid <= 0) {
                return [false, '请选择收货地址'];
            }

            $ad    = by::Address()->GetOneAddress($user_id, $aid);
            $isDel = $ad['is_del'] ?? 0; // 删除状态
            if (empty($ad) || ($isDel == by::Address()::IS_DEL['yes'])) { // 地址不存在、或地址被删除
                return [false, '请添加或选择收货地址'];
            }

            //todo 限购区域
            list($s, $m) = by::model('OfreightModel', 'goods')->IsDis($user_id, $aid);
            if (!$s) {
                return [-1, $m];
            }
        }


        //todo 判定该用户是否授权手机号
        $phone = by::Phone()->GetPhoneByUid($user_id);
        if (empty($phone)) {
            return [false, '请先授权手机号'];
        }

        //订单备注校验
        if (mb_strlen($data['note'] ?? '') > 50) {
            return [false, '请输入50字内备注'];
        }

        $platformIds = $data['platformIds'] ?? [];

        $indexGmainService = new IndexGoodsMainService();
        $goodsStockModel   = by::GoodsStockModel();

        $limit_gids = [];
        $gNums      = count($gcombines);

        $aData        = [];
        $tprice       = 0;
        $tpoint       = 0;
        $tcoupon      = 0;
        $tcouponPrice = 0;
        $tpointPrice  = 0;

        foreach ($gcombines as $k => $gcombine) {
            $gid = CUtil::uint($gcombine['gid'] ?? 0);
            $sid = CUtil::uint($gcombine['sid'] ?? 0);
            $num = CUtil::uint($gcombine['num'] ?? 0);
            if (empty($gid)) {
                return [false, '商品ID不存在！'];
            }
            // 活动限制
            list($status, $msg) = by::activityConfigModel()->activityFreePointsRule($user_id, $gid, $num);
            if (!$status) {
                return [false, ActivityConfigEnum::POINTS_ACTIVITY_RESULT[$msg] ?? '未知原因'];
            }

            if ($num <= 0) {
                return [false, '选择的商品数量不能为0！'];
            }

            $gInfo = $indexGmainService->GetOneByGidSid($gid, $sid, $platformIds, true, true);
            if (empty($gInfo)) {
                return [false, ($gNums > 1 ? '部分' : '') . '商品已下架，下单失败~'];
            }

            if ($checkStatus && $gInfo['status'] != 0) {
                return [false, ($gNums > 1 ? '部分' : '') . '商品已下架，下单失败~~'];
            }

            if ($gInfo['atype'] == $this->goodsMainModel::ATYPE['SPECS'] && empty($gInfo['spec'])) {
                return [false, '多规格商品，请选择正确的属性'];
            }


            // todo 商品限购
            if (isset($gInfo['limit_num']) && $gInfo['limit_num'] > 0) {
                $limit_gids[$gid]['limit_num'] = $gInfo['limit_num'];
                $limit_gids[$gid]['num']       = empty($limit_gids[$gid]['num']) ? $gcombine['num'] :
                        bcadd($limit_gids[$gid]['num'], $gcombine['num']);
            }

            //第一次校验库存
            if ($checkStock) {
                $sku   = $gInfo['spec']['sku'] ?? ($gInfo['sku'] ?? '');
                $stock = $goodsStockModel->OptStock($sku);
                if ($gcombine['num'] > $stock) {
                    return [false, ($gNums > 1 ? '部分' : '') . '商品库存不够，下单失败~'];
                }
            }


            //用户等级限制
            $levels = $gInfo['levels'] ?? '';
            if ($levels) {
                //获取用户等级
                $userLevel = strtolower($this->memberCenterModel->GetUserLevel($user_id));
                $levelArr  = explode('|', $levels);
                if (!in_array($userLevel, $levelArr)) return [false, '用户等级不符合，不允许购买!'];
            }

            //日期限制
            $online_time = $gInfo['online_time'] ?? '';
            if ($online_time && (strpos($online_time, '|') || strlen($online_time) < 10)) {
                $dn = date('w');
                if ($dn == 0) $dn = 7;
                $timeArr = explode('|', $online_time);
                if (!in_array($dn, $timeArr)) return [false, '今日不可买这个商品哟~'];
            } else {
                $online_time = CUtil::uint($online_time);
                if ($online_time > intval(START_TIME)) {
                    return [false, '尚未到购买时间！'];
                }
            }

            // 虚拟商品检查
            if ($gInfo['type'] == 2) {
                if ($num > 1&&isset($gInfo['subtype'])&&intval($gInfo['subtype'])==2){
                    return [false, '停车券每次只能购买一件！'];
                }

               if (isset($gInfo['subtype'])&&intval($gInfo['subtype'])>0){
                   list($coupon_status,$msg)= CouponService::getInstance()->check($gInfo['subtype'], $gInfo['sku'],$num);
                   if (!$coupon_status){
                       return [false, $msg];
                   }
               }
            }

            // 虚拟商品库存检查

            $exchange = CUtil::uint($gInfo['spec']['exchange'] ?? ($gInfo['exchange']) ?? 0);

            if ($exchange == $this->goodsPointsPriceModel::EXCHANGE['COUPON'] && (count($gcombines) > 1 || $num !== 1)) {
                return [false, '兑换券商品请直接购买！'];
            }

            $price        = $gInfo['spec']['price'] ?? $gInfo['price'];
            $point        = $gInfo['spec']['point'] ?? ($gInfo['point'] ?? 0);
            $coupon_id    = $gInfo['spec']['coupon_id'] ?? ($gInfo['coupon_id'] ?? 0);
            $coupon_price = 0;
            $sku          = $gInfo['spec']['sku'] ?? $gInfo['sku'];

            if ($exchange == $this->goodsPointsPriceModel::EXCHANGE['AP']) {
                $price = 0;
            } elseif ($exchange == $this->goodsPointsPriceModel::EXCHANGE['COUPON']) {
                if (empty($coupon_id)) return [false, '兑换券不能为空！'];
                $price        = $point = 0;
                $coupon_price = $gInfo['mprice'];
            } elseif ($exchange == $this->goodsPointsPriceModel::EXCHANGE['PRICE']) {
                if ($price <= 0) return [false, '商品价格不正确！'];
            } elseif ($exchange == $this->goodsPointsPriceModel::EXCHANGE['PAM']) {
                if ($point <= 0) return [false, '商品价格不正确！'];
                if ($price <= 0) return [false, '商品价格不正确！'];
            } elseif ($point <= 0 && $price <= 0) {
                return [false, '商品价格不正确！'];
            }

            $gInfo['tprice']       = bcmul($price, $num, 2);
            $gInfo['tpoint']       = bcmul($point, $num, 0);
            $coin_price            = by::point()->convert($gInfo['tpoint']);
            $gInfo['tpointPrice']  = $coin_price;
            $gInfo['tcoupon']      = CUtil::uint($coupon_id);
            $gInfo['tcouponPrice'] = $coupon_price;
            $gInfo['num']          = (string) $num;
            $gInfo['sku']          = $sku;

            $gcombines[$k]['tprice']       = CUtil::totalFee($gInfo['tprice']);
            $gcombines[$k]['tcouponPrice'] = CUtil::totalFee($gInfo['tcouponPrice']);
            $gcombines[$k]['tpointPrice']  = CUtil::totalFee($gInfo['tpointPrice']);
            $gcombines[$k]['tpoint']       = $gInfo['tpoint'];
            $gcombines[$k]['tcoupon']      = $gInfo['tcoupon'];
            $gcombines[$k]['sku']          = $gInfo['sku'];
            //判断商品总价
            $gcombines[$k]['toprice'] = !empty($gInfo['tcoupon']) ? $gcombines[$k]['tcouponPrice'] : bcadd($gcombines[$k]['tprice'], $gcombines[$k]['tpointPrice']);


            $aData[]      = $gInfo;
            $tprice       = bcadd($tprice, $gInfo['tprice'], 2);
            $tpointPrice  = bcadd($tpointPrice, $gInfo['tpointPrice'], 2);
            $tpoint       = bcadd($tpoint, $gInfo['tpoint'], 0);
            $tcoupon      = $gInfo['tcoupon'];
            $tcouponPrice = $gInfo['tcouponPrice'];
        }


        //商品类型校验
        $wares_sources = array_filter(array_unique(array_column($aData, 'source')));
        if (count($wares_sources) > 1) {
            return [false, '不同种类商品不允许叠加购买！'];
        }

        //是否实体商品校验
        $wares_types = array_filter(array_unique(array_column($aData, 'type')));
        if (count($wares_types) > 1) {
            return [false, '实体商品和虚拟商品不允许叠加兑换！'];
        }

        //是否要发货鉴定
        $wares_sends = array_filter(array_unique(array_column($aData, 'is_send')));
        if (count($wares_sends) > 1) {
            return [false, '不发货和发货商品不允许叠加兑换！'];
        }

        //是否包邮不算邮费
        $shipping  = 2; //包邮
        $shippings = array_filter(array_unique(array_column($aData, 'shipping')));
        if (in_array(1, $shippings)) {
            $shipping = 1;//不包邮
        }


        $ware_source = $wares_sources[0] ?? '';
        switch ($ware_source) {
            case $this->goodsMainModel::SOURCE['POINTS']:
                $order_type = $this->oMainModel::USER_ORDER_TYPE['POINT'];
                break;
            case $this->goodsMainModel::SOURCE['TRY_BEFORE_BUY']:
                $order_type = $this->oMainModel::USER_ORDER_TYPE['BAT'];
                break;
            default:
                return [false, '暂时不支持其他类型商品'];
        }

        //积分校验
        if ($checkPoint) {
            $mPoint = by::point();
            $tcoin  = $mPoint->get($user_id);
            if ($tcoin < $tpoint) return [false, '积分不足，快去赚取积分吧！'];
        }

        //优惠券校验
        if ($tcoupon) {
            $user_card_info = by::userCard()->getCardById($user_id, $tcoupon);
            if (empty($user_card_info)) return [false, '没有可使用的兑换券！'];
            $tpoint = 0 && $tprice = 0;
        }

        //todo 判断限购
        $goods_type = 2;
        if ($checkLimit && !empty($limit_gids)) {
            foreach ($limit_gids as $gid => $v) {
                list($s, $m) = by::Ogoods()->CanLimitBuy($user_id, $gid, $v['num'], $v['limit_num'], $goods_type);
                if (!$s) {
                    return [false, $m];
                }
            }
        }

        return [true, [
                'tprice'       => $tprice,
                'tpoint'       => $tpoint,
                'tcoupon'      => $tcoupon,
                'tcouponPrice' => $tcouponPrice,
                'tpointPrice'  => $tpointPrice,
                'aData'        => $aData,
                'gcombines'    => $gcombines,
                'order_type'   => $order_type,
                'shipping'     => $shipping,
        ]];
    }

    /**
     * @param $user_id
     * @param $sub_type
     * @param $order_no
     * @return array
     * 检查优惠券是否发放
     * @throws Exception
     */
    public function checkCoupon($user_id,$sub_type, $order_no): array
    {
        $user_id = CUtil::uint($user_id);
        if ($user_id <= 0) {
            return [false, '请先登录'];
        }

        if (empty($order_no)) {
            return [false, '请输入订单号'];
        }

        if (intval($sub_type) <= 0) {
            return [true, ''];
        }

        $count = 1;
        if ($sub_type == 1) {
            $count = by::userCard()->checkCouponSend($user_id, $order_no);
        } else if ($sub_type == 2) {
            $count = byNew::CouponCodeModel()::find()
                    ->where(['order_no' => $order_no, 'is_deleted' => 0])
                    ->count();
        }

        return $count > 0? [true, '已发放']: [false, '未发放'];
    }




}
