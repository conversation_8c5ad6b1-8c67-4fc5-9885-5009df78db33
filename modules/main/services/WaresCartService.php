<?php

namespace app\modules\main\services;

use app\models\by;
use app\models\byNew;
use app\models\CUtil;
use app\models\MyExceptionModel;
use app\modules\wares\services\goods\IndexGoodsMainService;
use RedisException;
use yii\db\Exception;

class WaresCartService
{
    private static $_instance = NULL;

    private function __construct()
    {
    }

    private function __clone()
    {
    }

    public static function getInstance()
    {
        if (is_null(self::$_instance)) {
            self::$_instance = new self();
        }
        return self::$_instance;
    }


    /**
     * @param $userId
     * @param $platformIds
     * @param $post
     * @return array
     * 添加商品到购物车
     */
    public function add($userId, $platformIds, $post): array
    {
        $db          = by::dbMaster();
        $transaction = $db->beginTransaction();

        try {
            // 验证商品数据
            $goodsJson = $post['goods_combines'] ?? [];
            if (empty($goodsJson)) {
                throw new MyExceptionModel('商品信息不能为空');
            }

            $goods = json_decode($goodsJson, true) ?? null;
            if (empty($goods) || !is_array($goods)) {
                throw new MyExceptionModel('参数有误,请重试！');
            }

            // 获取用户现有购物车数据
            $cartService   = byNew::WaresCartModel();
            $cartGoodsNums = $cartService->getCartGoodsNum($userId);

            $rows         = [];
            $goodsService = new IndexGoodsMainService();

            // 遍历商品进行校验和数据准备
            foreach ($goods as $value) {
                $gid = intval($value['gid'] ?? 0);
                $sid = intval($value['sid'] ?? 0);
                $num = intval($value['num'] ?? 0);

                if (empty($gid) || empty($num)) {
                    throw new MyExceptionModel('商品信息有误，请重试');
                }

                $goodsInfo = $goodsService->IndexMainInfoByGid(
                        $gid,
                        $platformIds,
                        true,
                        true,
                        true,
                        $userId
                );

                if (empty($goodsInfo)) {
                    throw new MyExceptionModel('商品不存在');
                }

                $num = CUtil::uint($num);
                if (empty($num)) {
                    throw new MyExceptionModel('请选择购买数量');
                }

                if ($sid && empty($goodsInfo['spec'])) {
                    throw new MyExceptionModel('商品属性不存在');
                }

                if ($goodsInfo['atype'] != by::GoodsMainModel()::ATYPE['SPEC'] && empty($sid)) {
                    throw new MyExceptionModel('请选择商品属性');
                }

                // 校验限购数量
                $cartGoodsNum = $cartGoodsNums[$gid][$sid] ?? 0;
                if ($goodsInfo['limit_num'] > 0 && ($num + $cartGoodsNum) > $goodsInfo['limit_num']) {
                    throw new MyExceptionModel("{$goodsInfo['name']}超出购买限制");
                }

                $rows[] = [
                        'user_id' => $userId,
                        'gid'     => $gid,
                        'sid'     => $sid,
                        'num'     => $num,
                        'ctime'   => intval(START_TIME)
                ];
            }

            // 批量保存数据
            $result = $cartService->batchSaveData($userId, $rows, $transaction);
            if (!$result) {
                throw new MyExceptionModel('添加购物车失败，请稍后再试');
            }

            $transaction->commit();
            // 清除缓存
            byNew::WaresCartModel()->clearCartCache($userId);
            return [true, '添加成功'];
        } catch (MyExceptionModel $e) {
            $transaction->rollBack();
            return [false, $e->getMessage()];
        } catch (\Exception $e) {
            $transaction->rollBack();
            $error = $e->getMessage() . "|" . $e->getFile() . ":" . $e->getLine();
            CUtil::debug($error, "error.wares_cart_service");
            return [false, '系统繁忙，请稍后再试'];
        }
    }


    /**
     * 删除用户购物车中的商品
     *
     * @param string $userId 用户ID
     * @param array $ids 要删除的商品ID数组
     * @return array [bool $success, string $message]
     */
    public function delete(string $userId, array $ids): array
    {
        try {
            // 验证用户ID
            if (empty($userId)) {
                return [false, '请授权登录后再来操作！'];
            }

            if (strlen($userId) > 11) {
                return [false, '用户ID格式不正确'];
            }

            // 验证商品ID数组
            if (empty($ids)) {
                return [false, '请选择要删除的商品'];
            }

            foreach ($ids as $id) {
                if (!is_numeric($id) || (int) $id <= 0) {
                    return [false, '商品ID格式不正确'];
                }
            }

            // 执行删除操作
            $db           = by::dbMaster();
            $deletedCount = $db->createCommand()->delete(
                    byNew::WaresCartModel()->tbName($userId),
                    ['id' => $ids, 'user_id' => $userId]
            )->execute();

            // 清除缓存
            byNew::WaresCartModel()->clearCartCache($userId);

            return [true, $deletedCount > 0 ? '删除成功' : '未找到要删除的商品'];
        } catch (Exception $e) {
            // 记录异常日志
            $error = $e->getMessage() . "|" . $e->getFile() . ":" . $e->getLine();
            CUtil::debug($error, 'error.wares_cart_service');
            return [false, '系统繁忙，请稍后再试'];
        } catch (RedisException $e) {
            // 记录Redis异常日志
            $error = $e->getMessage() . "|" . $e->getFile() . ":" . $e->getLine();
            CUtil::debug($error, 'error.wares_cart_redis_exception');
            return [false, '系统繁忙，请稍后再试'];
        }
    }

    /**
     * 获取购物车商品总数量
     * @param int $userId
     * @return array [status, data]
     */
    public function getCartCount($userId): array
    {
        try {
            // 验证用户ID
            $userId = CUtil::uint($userId);
            if (empty($userId)) {
                return [false, '用户信息不存在'];
            }

            $cartService = byNew::WaresCartModel();
            $count = $cartService->getCartCount($userId);

            return [true, ['count' => $count]];
        } catch (\Exception $e) {
            $error = $e->getMessage() . "|" . $e->getFile() . ":" . $e->getLine();
            CUtil::debug($error, "error.wares_cart_service");
            return [false, '系统繁忙，请稍后再试'];
        }
    }

    /**
     * 修改购物车商品数量
     * @param int $userId
     * @param int $cartId
     * @param int $num
     * @return array [status, message]
     */
    public function modifyCart($userId, $cartId, $num): array
    {
        $db = by::dbMaster();
        $transaction = $db->beginTransaction();

        try {
            // 验证参数
            $userId = CUtil::uint($userId);
            $cartId = CUtil::uint($cartId);
            $num = CUtil::uint($num);

            if (empty($userId)) {
                throw new MyExceptionModel('用户信息不存在');
            }

            if (empty($cartId)) {
                throw new MyExceptionModel('购物车商品ID不能为空');
            }

            if (empty($num)) {
                throw new MyExceptionModel('商品数量必须大于0');
            }

            $cartService = byNew::WaresCartModel();

            // 获取购物车商品信息
            $cart = $cartService->getCartList($userId);
            $cartList = $cart['list'] ?? [];
            $total = $cart['total'] ?? 0;
            $cartItem = null;
            foreach ($cartList as $item) {
                if ($item['id'] == $cartId) {
                    $cartItem = $item;
                    break;
                }
            }

            if (empty($cartItem)) {
                throw new MyExceptionModel('购物车商品不存在');
            }

            // 验证商品是否还存在且可购买
            $goodsService = new IndexGoodsMainService();
            $goodsInfo = $goodsService->IndexMainInfoByGid(
                $cartItem['gid'],
                [],
                true,
                true,
                true,
                $userId
            );

            if (empty($goodsInfo)) {
                throw new MyExceptionModel('商品不存在或已下架');
            }

            // 校验限购数量（排除当前购物车商品的数量）
            if ($goodsInfo['limit_num'] > 0) {
                $cartGoodsNums = $cartService->getCartGoodsNum($userId);
                $currentCartNum = $cartGoodsNums[$cartItem['gid']][$cartItem['sid']] ?? 0;
                $otherCartNum = $currentCartNum - $cartItem['num']; // 其他购物车中的数量

                if (($num + $otherCartNum) > $goodsInfo['limit_num']) {
                    throw new MyExceptionModel("{$goodsInfo['name']}超出购买限制");
                }
            }

            // 修改购物车数量
            $result = $cartService->modifyCartNum($userId, $cartId, $num);
            if (!$result) {
                throw new MyExceptionModel('修改购物车失败，请稍后再试');
            }

            $transaction->commit();

            // 清除缓存
            $cartService->clearCartCache($userId);

            return [true, '修改成功'];
        } catch (MyExceptionModel $e) {
            $transaction->rollBack();
            return [false, $e->getMessage()];
        } catch (\Exception $e) {
            $transaction->rollBack();
            $error = $e->getMessage() . "|" . $e->getFile() . ":" . $e->getLine();
            CUtil::debug($error, "error.wares_cart_service");
            return [false, '系统繁忙，请稍后再试'];
        }
    }

    /**
     * 获取购物车列表（包含商品详情）
     * @param int $userId
     * @param array $platformIds
     * @param int $page
     * @param int $pageSize
     * @return array [status, data]
     */
    public function getCartList($userId, $platformIds = [], $page = 1, $pageSize = 20): array
    {
        try {
            // 验证用户ID
            $userId = CUtil::uint($userId);
            if (empty($userId)) {
                return [false, '用户信息不存在'];
            }

            $cartService = byNew::WaresCartModel();
            $cartList = $cartService->getCartList($userId, $page, $pageSize);

            if (empty($cartList['list'])) {
                return [true, [
                    'list' => [],
                    'count' => 0,
                    'page' => $page,
                    'page_size' => $pageSize,
                    'total_page' => 0
                ]];
            }

            $goodsService = new IndexGoodsMainService();
            $result = [];

            foreach ($cartList['list'] as $cartItem) {
                // 获取商品详情
                $goodsInfo = $goodsService->IndexMainInfoByGid(
                    $cartItem['gid'],
                    $platformIds,
                    true,
                    true,
                    true,
                    $userId
                );

                if (empty($goodsInfo)) {
                    // 商品不存在，跳过
                    continue;
                }

                // 组装购物车商品信息
                $cartGoodsInfo = [
                    'cart_id' => $cartItem['id'],
                    'gid' => $cartItem['gid'],
                    'sid' => $cartItem['sid'],
                    'num' => $cartItem['num'],
                    'ctime' => $cartItem['ctime'],
                    'goods_name' => $goodsInfo['name'] ?? '',
                    'goods_image' => $goodsInfo['image'] ?? '',
                    'goods_price' => $goodsInfo['price'] ?? 0,
                    'goods_mprice' => $goodsInfo['mprice'] ?? 0,
                    'goods_stock' => $goodsInfo['stock'] ?? 0,
                    'goods_status' => $goodsInfo['status'] ?? 0,
                    'limit_num' => $goodsInfo['limit_num'] ?? 0,
                    'spec_info' => $goodsInfo['spec'] ?? [],
                ];

                $result[] = $cartGoodsInfo;
            }

            return [true, [
                'list' => $result,
                'count' => $cartList['total'],
                'page' => $page,
                'page_size' => $pageSize,
                'total_page' => ceil($cartList['total'] / $pageSize)
            ]];
        } catch (\Exception $e) {
            $error = $e->getMessage() . "|" . $e->getFile() . ":" . $e->getLine();
            CUtil::debug($error, "error.wares_cart_service");
            return [false, '系统繁忙，请稍后再试'];
        }
    }

    /**
     * 删除购物车商品
     * @param int $userId
     * @param array $cartIds
     * @return array [status, message]
     */
    public function deleteCart($userId, $cartIds): array
    {
        $db = by::dbMaster();
        $transaction = $db->beginTransaction();

        try {
            // 验证参数
            $userId = CUtil::uint($userId);
            if (empty($userId)) {
                throw new MyExceptionModel('用户信息不存在');
            }

            if (empty($cartIds) || !is_array($cartIds)) {
                throw new MyExceptionModel('请选择要删除的商品');
            }

            // 过滤和验证购物车ID
            $validCartIds = [];
            foreach ($cartIds as $cartId) {
                $cartId = CUtil::uint($cartId);
                if ($cartId > 0) {
                    $validCartIds[] = $cartId;
                }
            }

            if (empty($validCartIds)) {
                throw new MyExceptionModel('请选择要删除的商品');
            }

            $cartService = byNew::WaresCartModel();

            // 删除购物车商品
            $result = $cartService->deleteCartItems($userId, $validCartIds);
            if (!$result) {
                throw new MyExceptionModel('删除失败，请稍后再试');
            }

            $transaction->commit();
            return [true, '删除成功'];
        } catch (MyExceptionModel $e) {
            $transaction->rollBack();
            return [false, $e->getMessage()];
        } catch (\Exception $e) {
            $transaction->rollBack();
            $error = $e->getMessage() . "|" . $e->getFile() . ":" . $e->getLine();
            CUtil::debug($error, "error.wares_cart_service");
            return [false, '系统繁忙，请稍后再试'];
        }
    }


    /**
     * 验证并清理购物车ID
     * @param string $cartIds JSON格式的购物车ID字符串
     * @return array 清理后的购物车ID数组
     */
    public function validateAndCleanCartIds(string $cartIds): array
    {
        if (empty($cartIds)) {
            return [];
        }

        $decoded = json_decode($cartIds, true);
        if (!is_array($decoded)) {
            return [];
        }

        return array_filter($decoded, 'is_numeric');
    }
}