<?php


namespace app\modules\main\controllers;


use app\components\AppNRedisKeys;
use app\constants\RespStatusCodeConst;
use app\exceptions\DrawActivityException;
use app\models\by;
use app\models\byNew;
use app\models\CUtil;
use app\modules\goods\services\DrawActivityService;
use app\modules\main\enums\activity\ActivityConfigEnum;

class DrawController extends CommController
{

    /**
     * @OA\Post(
     *     path="/main/draw/activity-list",
     *     summary="抽奖活动-活动详情",
     *     description="抽奖活动-活动详情",
     *     tags={"抽奖活动"},
     *     security={
     *         {"userid": {}},
     *         {"sessid": {}},
     *     },
     *     @OA\RequestBody(
     *         @OA\MediaType(
     *             mediaType="application/x-www-form-urlencoded",
     *             @OA\JsonContent(
     *                 allOf={
     *                     @OA\Schema(ref="#/components/schemas/BaseRequest"),
     *                     @OA\Schema(
     *                         @OA\Property(property="ac_id", type="integer", default="", description="活动ID")
     *                     )
     *                 }
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response="200",
     *         description="状态码、提示消息",
     *         @OA\JsonContent(
     *             required={"iRet", "sMsg", "data"},
     *             @OA\Property(property="iRet", type="integer", description="状态码"),
     *             @OA\Property(property="sMsg", type="string", description="提示消息"),
     *             @OA\Property(property="data", type="array", description="数据", @OA\Items())
     *         )
     *     )
     * )
     */
    public function actionActivityList()
    {

        $post = \Yii::$app->request->post();

        $acId = intval($post['ac_id'] ?? 0);

        list($s, $ret) = DrawActivityService::getInstance()->getActivityDetail($acId);

        if (!$s) {
            CUtil::json_response(-1, $ret);
        }

        CUtil::json_response(1, 'ok', $ret);
    }


    /**
     * @OA\Post(
     *     path="/main/draw/draw-times",
     *     summary="抽奖活动-获取抽奖次数",
     *     description="抽奖活动-获取抽奖次数",
     *     tags={"抽奖活动"},
     *     security={
     *         {"userid": {}},
     *         {"sessid": {}},
     *     },
     *     @OA\RequestBody(
     *         @OA\MediaType(
     *             mediaType="application/x-www-form-urlencoded",
     *             @OA\JsonContent(
     *                 allOf={
     *                     @OA\Schema(ref="#/components/schemas/BaseRequest"),
     *                     @OA\Schema(
     *                         @OA\Property(property="ac_id", type="integer", default="", description="活动ID")
     *                     )
     *                 }
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response="200",
     *         description="状态码、提示消息",
     *         @OA\JsonContent(
     *             required={"iRet", "sMsg", "data"},
     *             @OA\Property(property="iRet", type="integer", description="状态码"),
     *             @OA\Property(property="sMsg", type="string", description="提示消息"),
     *             @OA\Property(property="data", type="array", description="数据", @OA\Items())
     *         )
     *     )
     * )
     */
    public function actionDrawTimes()
    {
        $post   = \Yii::$app->request->post();
        $acId = intval($post['ac_id'] ?? 0);//活动ID

        $userId = $this->user_id;

        if (strlen($userId) > 11 || empty(CUtil::uint($userId))) {
            // 获取活动信息
            $activityDetail = byNew::DrawActivity()->getActivityDetail($acId);
            $dailyFreeTimes = intval($activityDetail['daily_free_times'] ?? 1);
            CUtil::json_response(1, 'ok',$dailyFreeTimes );
        }



        list($s, $ret) = DrawActivityService::getInstance()->getUserDrawTimes($userId, $acId);
        if (!$s) {
            CUtil::json_response(-1, $ret);
        }

        CUtil::json_response(1, 'ok', $ret);
    }


    /**
     * @OA\Post(
     *     path="/main/draw/do-activity-task",
     *     summary="抽奖活动-每日赠送抽奖次数",
     *     description="抽奖活动-每日赠送抽奖次数",
     *     tags={"抽奖活动"},
     *     security={
     *         {"userid": {}},
     *         {"sessid": {}},
     *     },
     *     @OA\RequestBody(
     *         @OA\MediaType(
     *             mediaType="application/x-www-form-urlencoded",
     *             @OA\JsonContent(
     *                 allOf={
     *                     @OA\Schema(ref="#/components/schemas/BaseRequest"),
     *                     @OA\Schema(
     *                         @OA\Property(property="task_code", type="integer", default="", description="任务CODE")
     *                     )
     *                 }
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response="200",
     *         description="状态码、提示消息",
     *         @OA\JsonContent(
     *             required={"iRet", "sMsg", "data"},
     *             @OA\Property(property="iRet", type="integer", description="状态码"),
     *             @OA\Property(property="sMsg", type="string", description="提示消息"),
     *             @OA\Property(property="data", type="array", description="数据", @OA\Items())
     *         )
     *     )
     * )
     */
    public function actionDoActivityTask()
    {
        $user_id = $this->user_id;
        if (strlen($user_id) > 11 || empty(CUtil::uint($user_id))) {
            CUtil::json_response(-1, '请先授权，再完成任务！');
        }

        $unique_key = CUtil::getAllParams(__FUNCTION__);

        list($s) = by::model("CommModel", MAIN_MODULE)->AccFrequency($this->user_id, $unique_key, 1, "EX", 1);
        if (!$s) {
            CUtil::json_response(-1, '频繁请求，请稍候！');
        }

        $post = \Yii::$app->request->post();

        list($s, $ret) = DrawActivityService::getInstance()->doActivityTask($user_id, $post);

        if (!$s) {
            CUtil::json_response(-1, $ret);
        }

        CUtil::json_response(1, 'ok', $ret);
    }

    /**
     * @OA\Post(
     *     path="/main/draw/do-draw-activity",
     *     summary="抽奖活动-抽奖",
     *     description="抽奖活动-抽奖",
     *     tags={"抽奖活动"},
     *     security={
     *         {"userid": {}},
     *         {"sessid": {}},
     *     },
     *     @OA\RequestBody(
     *         @OA\MediaType(
     *             mediaType="application/x-www-form-urlencoded",
     *             @OA\JsonContent(
     *                 allOf={
     *                     @OA\Schema(ref="#/components/schemas/BaseRequest"),
     *                     @OA\Schema(
     *                         @OA\Property(property="ac_id", type="integer", default="", description="活动ID")
     *                     )
     *                 }
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response="200",
     *         description="状态码、提示消息",
     *         @OA\JsonContent(
     *             required={"iRet", "sMsg", "data"},
     *             @OA\Property(property="iRet", type="integer", description="状态码"),
     *             @OA\Property(property="sMsg", type="string", description="提示消息"),
     *             @OA\Property(property="data",type="object",ref="#/components/schemas/DoDrawActivity",description="数据")
     *         )
     *     )
     * )
     */
    public function actionDoDrawActivity()
    {
        // 1、用户ID
        $user_id = $this->user_id;
        if (strlen($user_id) > 11 || empty(CUtil::uint($user_id))) {
            CUtil::json_response(-1, '请先授权，再抽奖！');
        }

        //校验活动时间
        // if(!ActivityConfigEnum::judgeActivityTime($user_id,'DRAW')){
        //     CUtil::json_response(-1, '活动已结束！');
        // }

        // 2、频率限制：3秒钟访问一次
        $unique_key = CUtil::getAllParams(__FUNCTION__);
        list($s) = by::model("CommModel", MAIN_MODULE)->AccFrequency($this->user_id, $unique_key, 3, "EX", 1);
        if (!$s) {
            CUtil::json_response(-1, '频繁请求，请稍候！');
        }

        // 3、活动ID
        $post = \Yii::$app->request->post();
        $acId = intval($post['ac_id'] ?? 0);

        // 4、抽奖
        try {
            $ret = DrawActivityService::getInstance()->doDrawActivity($acId, $user_id);
            CUtil::json_response(1, 'ok', $ret);
        } catch (DrawActivityException $e) {
            CUtil::json_response(-1, $e->getMessage());
        } catch (\Exception $e) {
            CUtil::json_response(-1, '抽奖失败');
            // 记录日志
            CUtil::debug("抽奖失败异常信息：file:{$e->getFile()}, err line:{$e->getLine()}, err message:{$e->getMessage()}, err code:{$e->getCode()}", "err.draw.activity");
        }
    }

    /**
     * @OA\Post(
     *     path="/main/draw/draw-record",
     *     summary="抽奖活动-中奖记录",
     *     description="抽奖活动-中奖记录",
     *     tags={"抽奖活动"},
     *     security={
     *         {"userid": {}},
     *         {"sessid": {}},
     *     },
     *     @OA\RequestBody(
     *         @OA\MediaType(
     *             mediaType="application/x-www-form-urlencoded",
     *             @OA\JsonContent(
     *                 allOf={
     *                     @OA\Schema(ref="#/components/schemas/BaseRequest"),
     *                     @OA\Schema(
     *                         @OA\Property(property="ac_id", type="integer", default="", description="活动ID"),
     *                         @OA\Property(property="page", type="integer", default="1", description="第几页"),
     *                         @OA\Property(property="page_size", type="integer", default="20", description="每一页数据量")
     *                     )
     *                 }
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response="200",
     *         description="状态码、提示消息",
     *         @OA\JsonContent(
     *             required={"iRet", "sMsg", "data"},
     *             @OA\Property(property="iRet", type="integer", description="状态码"),
     *             @OA\Property(property="sMsg", type="string", description="提示消息"),
     *             @OA\Property(property="data", type="array", @OA\Items(ref="#/components/schemas/DrawRecordDetail"), description="中奖记录")
     *         )
     *     )
     * )
     */
    public function actionDrawRecord()
    {
        // 用户ID
        $user_id = $this->user_id;
        if (strlen($user_id) > 11 || empty(CUtil::uint($user_id))) {
            CUtil::json_response(-1, '请先授权，再查看中奖记录！');
        }

        // 活动ID
        $post = \Yii::$app->request->post();
        $acId = intval($post['ac_id'] ?? 0);
        if (empty($acId)) {
            CUtil::json_response(1, 'ok', []);
        }

        // 分页
        $page = intval($post['page'] ?? 1);
        $pageSize = intval($post['page_size'] ?? 20);

        // 查询结果
        $ret = DrawActivityService::getInstance()->getDrawRecord($acId, $user_id, $page, $pageSize);
        CUtil::json_response(1, 'ok', $ret);
    }


    /**
     * @OA\Post(
     *     path="/main/draw/task-list",
     *     summary="抽奖活动-任务列表",
     *     description="抽奖活动-任务列表",
     *     tags={"抽奖活动"},
     *     security={
     *         {"userid": {}},
     *         {"sessid": {}},
     *     },
     *     @OA\RequestBody(
     *         @OA\MediaType(
     *             mediaType="application/x-www-form-urlencoded",
     *             @OA\JsonContent(
     *                 allOf={
     *                     @OA\Schema(ref="#/components/schemas/BaseRequest"),
     *                     @OA\Schema(
     *                         @OA\Property(property="ac_id", type="integer", default="", description="任务CODE")
     *                     )
     *                 }
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response="200",
     *         description="状态码、提示消息",
     *         @OA\JsonContent(
     *             required={"iRet", "sMsg", "data"},
     *             @OA\Property(property="iRet", type="integer", description="状态码"),
     *             @OA\Property(property="sMsg", type="string", description="提示消息"),
     *             @OA\Property(property="data", type="array", description="数据", @OA\Items())
     *         )
     *     )
     * )
     */
    public function actionTaskList()
    {
        $post = \Yii::$app->request->post();

        $acId = intval($post['ac_id'] ?? 0);

        if (empty($acId)) {
            CUtil::json_response(-1, '活动ID有误');
        }

        $userId = $this->user_id;

        list($s, $ret) = DrawActivityService::getInstance()->getTaskList($userId, $acId);

        if (!$s) {
            CUtil::json_response(-1, $ret);
        }

        CUtil::json_response(1, 'ok', $ret);
    }


    public function actionDoNewDrawActivity()
    {
        // 1、用户ID
        $user_id = $this->user_id;
        if (strlen($user_id) > 11 || empty(CUtil::uint($user_id))) {
            CUtil::json_response(-1, '请先授权，再抽奖！');
        }

        // 2、频率限制：3秒钟访问一次
        $unique_key = CUtil::getAllParams(__FUNCTION__);
        list($s) = by::model("CommModel", MAIN_MODULE)->AccFrequency($this->user_id, $unique_key, 3, "EX", 1);
        if (!$s) {
            CUtil::json_response(1, '频繁请求，请稍候！', [
                    'code'    => 1010,
                    'message' => '频繁请求，请稍候！',
                    'result'  => [],
            ]);
        }

        // 3、活动ID
        $post = \Yii::$app->request->post();
        $acRelationId = intval($post['ac_id'] ?? 0);

        // 4、抽奖
        $ret = DrawActivityService::getInstance()->doNewDrawActivity($acRelationId, intval($user_id));
        if ($ret['status'] != 1) {
            CUtil::json_response(-1, $ret['message']);
        }
        CUtil::json_response(1, $ret['message'], [
                'code'    => $ret['code'],
                'message' => $ret['message'],
                'result'  => $ret['data'],
        ]);
    }


    public function actionStock()
    {
        $acRelationId = \Yii::$app->request->post('ac_id',0);
        if (empty($acRelationId)){
            CUtil::json_response(-1,"活动有误，请检查~");
        }

        $key = AppNRedisKeys::envelopeActivity($acRelationId);

        $redis = by::redis();  // 修正组件调用方式

        // 事务开始：确保活动校验和奖品抽取的原子性

        // 检查活动库存
        $listLength = $redis->lLen($key);

        // 校验活动库存
        if ($listLength <= 0) {
            CUtil::json_response(1,"奖品已抽完",[
                    "code"=>2,//没库存
            ]);

        }
        CUtil::json_response(1,"ok",[
                "code"=>1,//有库存
        ]);
    }

    public function actionNewDrawRecord()
    {
        // 用户ID
        $user_id = $this->user_id;
        if (strlen($user_id) > 11 || empty(CUtil::uint($user_id))) {
            CUtil::json_response(-1, '请先授权，再查看中奖记录！');
        }

        // 活动ID
        $post         = \Yii::$app->request->post();
        $acRelationId = intval($post['ac_id'] ?? 0);
        if (empty($acRelationId)) {
            CUtil::json_response(1, 'ok', []);
        }

        // 分页
        $page     = intval($post['page'] ?? 1);
        $pageSize = intval($post['page_size'] ?? 20);

        // 增加缓存
        $redis    = by::redis();
        $redisKey = AppNRedisKeys::newDrawActivityRecord($acRelationId);
        $subKey   = CUtil::getNewAllParams("users", $user_id);

        // 如果缓存中有数据，直接返回
        $jsonResponse = $redis->hget($redisKey, $subKey);

        if (!empty($jsonResponse)) {
            $data = json_decode($jsonResponse, true);
            CUtil::json_response(1, 'ok', $data);
        }

        // 查询结果
        $ret = DrawActivityService::getInstance()->getEnvelopeRecord($acRelationId, $user_id, $page, $pageSize);
        // 避免频繁查询数据库
        $redis->hset($redisKey, $subKey, json_encode($ret, JSON_UNESCAPED_UNICODE)); // 缓存1天
        CUtil::json_response(1, 'ok', $ret);
    }


    public function actionCheckNewUser()
    {
        $user_id = $this->user_id;
        if (strlen($user_id) > 11 || empty(CUtil::uint($user_id))) {
            CUtil::json_response(-1, '请先授权，再查看是否新用户！');
        }

        // 检查用户是否新用户
        list($status, $message, $data) = DrawActivityService::getInstance()->isNewUser($user_id);
        CUtil::json_response(RespStatusCodeConst::SUCCESS_CODE, $message, $data);
    }
}
