<?php

namespace app\modules\main\controllers;

use app\models\CUtil;
use app\models\by;
use app\models\byNew;
use app\components\WeiXin;
use app\components\AliYunOss;
use app\components\AppCRedisKeys;
use app\jobs\EmployeeInviteJob;
use app\jobs\EmployeeOptScoreJob;
use app\modules\main\services\UserBindService;


class UserBindController extends CommController
{
    /**
     * 绑定列表
     * @return void
     */
    public function actionList()
    {
        // 获取参数
        $params = \Yii::$app->request->post();
        $res = [];
        CUtil::json_response(1, 'ok', $res);
    }
    public function actionAppShare(){
        $params = \Yii::$app->request->post();
        $user_info = by::users()->getOneByUid($this->user_id);

        $res = [
            'uid'=>$user_info['user_id'],
            'username'=>$user_info['nick'],
            'avatar'=>$user_info['avatar'],
            'card_type'=>'bind_employee'
        ];
        $uid = by::Phone()->getUidByUserId($this->user_id);
        if (empty($uid)) {
            CUtil::debug('event:EMPLOYEE_BUY_GOODS|user_id:' . $this->user_id . '|用户uid为空', 'warn.uid');
            CUtil::json_response(-1,'分享链接生成失败');
        }

        $query = ['employee_uid'=>$uid];
        $queryStr = http_build_query($query, '', '&', PHP_QUERY_RFC3986);
        //生产分享链接
        list($s, $url) = WeiXin::factory()->UrlLink('/pages/index/index', $queryStr);
        if (!$s) {
            CUtil::json_response(-1,'分享链接生成失败');
        }
        $res['url'] = $url;

        CUtil::json_response(1, 'ok', $res);
    }

    public function actionShare(){
        $params = \Yii::$app->request->post();
        $user_info = by::users()->getOneByUid($this->user_id);

        $res = [
            'uid'=>$user_info['user_id'],
            'username'=>$user_info['nick'],
            'avatar'=>$user_info['avatar'],
            'card_type'=>'bind_employee'
        ];
        $uid = by::Phone()->getUidByUserId($this->user_id);
        if (empty($uid)) {
            CUtil::debug('event:EMPLOYEE_BUY_GOODS|user_id:' . $this->user_id . '|用户uid为空', 'warn.uid');
            CUtil::json_response(-1,'太阳码生成失败');
        }

        // 先去缓存里取
        $redis       = by::redis('core');
        $session_key = AppCRedisKeys::getEmployeeShareCard('bind_employee', $this->user_id);
        $share_card_img = $redis->get($session_key);
        if($share_card_img){
            $res['card_img'] = $share_card_img;
            CUtil::json_response(1, 'ok', $res);
        }else{
            list($s, $sunCode) = WeiXin::factory()->getWxaCodeUnlimit("pages/index/index", 'employee_uid=' . $uid,false);
            if($s && $sunCode){
                list($s, $shopCodeImg) = AliYunOss::factory()->uploadBinaryImage($sunCode, "png");
                $res['card_img'] = $shopCodeImg;
                $redis->set($session_key, $shopCodeImg, ['EX' => CUtil::timeUntilNextSunday()]);
                // 验证是否是员工
                $user = byNew::UserEmployeeModel()->getEmployeeInfo($uid);
                if ($user){
                    \Yii::$app->queue->push(new EmployeeOptScoreJob([
                        'params' => [
                            'uid'         => $uid,
                            'score'       => 200,
                            'handle_type' => 1,
                            'score_type'  => 3,
                            'handle_name' => '',
                            'source'      => '分享“微笑大使”个人专属二维码'
                        ]
                    ]));
                    $res['tips'] = '完成任务，获得200微笑分';
                }else{
                    $res['tips'] = '分享成功';
                }
                
                CUtil::json_response(1, 'ok', $res);
            }
        }
        CUtil::json_response(-1,'太阳码生成失败');
        
    }


    public function actionBind(){
        $params = \Yii::$app->request->post();
        
        // 绑定员工
        $employeeUid = $params['employee_uid'];
        $isNew = $params['is_new'] ?? 0;
        // 当前用户uid
        // $uid = $this->user_id;
        $uid = by::Phone()->getUidByUserId($this->user_id);

        //处理绑定操作
        $error = '';
        $res = UserBindService::getInstance()->bindEmployee($uid, $employeeUid, $isNew, $error);
        if(!$res){
            // 绑定失败，记录日志
            CUtil::debug('用户绑定员工失败:'.$this->user_id.'-'.$employeeUid.'-'.$error,'user_bind.info');
        }
        CUtil::json_response(1, '操作成功');
    }
}