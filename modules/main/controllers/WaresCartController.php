<?php

namespace app\modules\main\controllers;


use app\models\by;
use app\models\CUtil;
use app\modules\cart\services\CartService;
use app\modules\main\services\WaresCartService;
use app\modules\wares\services\goods\GoodsSpecsService;
use app\modules\wares\services\goods\GoodsStockService;
use app\modules\wares\services\goods\IndexGoodsMainService;
use yii\db\Exception;

class WaresCartController extends CommController
{


    /**
     * 添加购物车
     * @OA\Post(
     *     path="/main/wares-cart/add",
     *     summary="添加商品到购物车",
     *     description="将指定商品添加到用户的购物车中",
     *     tags={"积分商城购物车"},
     *     security={
     *         {"sessid": {}},
     *     },
     *     @OA\RequestBody(
     *         @OA\MediaType(mediaType="application/x-www-form-urlencoded",
     *             @OA\Schema(
     *                 @OA\Property(property="goods_id",type="integer",description="商品ID"),
     *                 @OA\Property(property="num",type="integer",description="商品数量"),
     *                 @OA\Property(property="spec_id",type="integer",description="规格ID")
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response="200",
     *         description="状态码、提示消息",
     *        @OA\JsonContent(
     *             type="object",
     *              @OA\Property(property="iRet",type="integer",description="状态码"),
     *              @OA\Property(property="sMsg",type="string",description="提示消息"),
     *              @OA\Property(property="data",type="object",description="数据")
     *         )
     *     )
     * )
     */
    public function actionAdd()
    {
        // 验证用户ID
        $userId = $this->user_id;
        if (empty($userId) || strlen($userId) > 11) {
            CUtil::json_response(-1, '请授权登录后再来操作！');
        }

        // 获取POST数据
        $post = \Yii::$app->request->post();

        // 添加商品到购物车
        list($status, $message) = WaresCartService::getInstance()->add($userId, $this->platformIds, $post);

        // 返回结果
        CUtil::json_response(
                $status ? 1 : -1,
                $status ? '添加成功' : $message
        );
    }


    /**
     * 删除购物车商品
     * @OA\Post(
     *     path="/main/wares-cart/delete",
     *     summary="删除购物车中的商品",
     *     description="删除用户购物车中指定的商品",
     *     tags={"积分商城购物车"},
     *     security={
     *         {"sessid": {}},
     *     },
     *     @OA\RequestBody(
     *         @OA\MediaType(mediaType="application/x-www-form-urlencoded",
     *             @OA\Schema(
     *                 @OA\Property(property="ids",type="string",description="要删除的购物车商品ID，多个ID用逗号分隔")
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response="200",
     *         description="状态码、提示消息",
     *        @OA\JsonContent(
     *             type="object",
     *              @OA\Property(property="iRet",type="integer",description="状态码"),
     *              @OA\Property(property="sMsg",type="string",description="提示消息"),
     *              @OA\Property(property="data",type="object",description="数据")
     *         )
     *     )
     * )
     */
    public function actionDelete()
    {
        $ids = \Yii::$app->request->post('ids', '');
        if (empty($ids)) {
            CUtil::json_response(-1, '请选择要删除的商品');
        }
        $userId = $this->user_id;
        if (empty($userId) || strlen($userId) > 11) {
            CUtil::json_response(-1, '请授权登录后再来操作！');
        }
        $ids = explode(',', $ids);
        list($status, $message) = WaresCartService::getInstance()->delete($userId, $ids);
        // 返回结果
        CUtil::json_response(
                $status ? 1 : -1,
                $status ? '删除成功' : $message
        );
    }


    /**
     * 购物车数量
     * @OA\Post(
     *     path="/main/wares-cart/count",
     *     summary="获取购物车商品总数量",
     *     description="获取用户购物车中商品的总数量",
     *     tags={"积分商城购物车"},
     *     security={
     *         {"sessid": {}},
     *     },
     *     @OA\Response(
     *         response="200",
     *         description="状态码、提示消息",
     *        @OA\JsonContent(
     *             type="object",
     *              @OA\Property(property="iRet",type="integer",description="状态码"),
     *              @OA\Property(property="sMsg",type="string",description="提示消息"),
     *              @OA\Property(property="data",type="object",description="数据",
     *                  @OA\Property(property="count",type="integer",description="购物车商品总数量")
     *              )
     *         )
     *     )
     * )
     */
    public function actionCount()
    {
        // 验证用户ID
        $userId = $this->user_id;
        if (empty($userId) || strlen($userId) > 11) {
            CUtil::json_response(-1, '请授权登录后再来操作！');
        }

        // 获取购物车数量
        list($status, $result) = WaresCartService::getInstance()->getCartCount($userId);

        // 返回结果
        CUtil::json_response(
            $status ? 1 : -1,
            $status ? 'OK' : $result,
            $status ? $result : []
        );
    }

    /**
     * 修改购物车
     * @OA\Post(
     *     path="/main/wares-cart/modify",
     *     summary="修改购物车商品数量",
     *     description="修改购物车中指定商品的数量",
     *     tags={"积分商城购物车"},
     *     security={
     *         {"sessid": {}},
     *     },
     *     @OA\RequestBody(
     *     @OA\MediaType(  mediaType="application/x-www-form-urlencoded",
     *       @OA\JsonContent(
     *          @OA\Property(property="cart_id",type="integer",description="购物车商品ID"),
     *          @OA\Property(property="num",type="integer",description="商品数量")
     *       )
     *     )
     * ),
     *     @OA\Response(
     *         response="200",
     *         description="状态码、提示消息",
     *        @OA\JsonContent(
     *             type="object",
     *              @OA\Property(property="iRet",type="integer",description="状态码"),
     *              @OA\Property(property="sMsg",type="string",description="提示消息"),
     *              @OA\Property(property="data",type="object",description="数据")
     *         )
     *     )
     * )
     */
    public function actionModify()
    {
        // 验证用户ID
        $userId = $this->user_id;
        if (empty($userId) || strlen($userId) > 11) {
            CUtil::json_response(-1, '请授权登录后再来操作！');
        }

        // 获取POST数据
        $post = \Yii::$app->request->post();
        $cartId = intval($post['cart_id'] ?? 0);
        $num = intval($post['num'] ?? 0);

        // 参数验证
        if (empty($cartId)) {
            CUtil::json_response(-1, '购物车商品ID不能为空');
        }

        if (empty($num)) {
            CUtil::json_response(-1, '商品数量必须大于0');
        }

        // 修改购物车
        list($status, $message) = WaresCartService::getInstance()->modifyCart($userId, $cartId, $num);

        // 返回结果
        CUtil::json_response(
            $status ? 1 : -1,
            $status ? '修改成功' : $message
        );
    }

    /**
     * 购物车列表
     * @OA\Post(
     *     path="/main/wares-cart/list",
     *     summary="获取购物车商品列表",
     *     description="获取用户购物车中的商品列表，包含商品详情信息",
     *     tags={"积分商城购物车"},
     *     security={
     *         {"sessid": {}},
     *     },
     *     @OA\RequestBody(
     *         @OA\MediaType(mediaType="application/x-www-form-urlencoded",
     *             @OA\Schema(
     *                 @OA\Property(property="page",type="integer",description="页码，默认1"),
     *                 @OA\Property(property="page_size",type="integer",description="每页数量，默认20")
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response="200",
     *         description="状态码、提示消息",
     *        @OA\JsonContent(
     *             type="object",
     *              @OA\Property(property="iRet",type="integer",description="状态码"),
     *              @OA\Property(property="sMsg",type="string",description="提示消息"),
     *              @OA\Property(property="data",type="object",description="数据",
     *                  @OA\Property(
     *                      property="list",
     *                      type="array",
     *                      description="购物车商品列表",
     *                      @OA\Items(
     *                          type="object",
     *                          @OA\Property(property="cart_id",type="string",description="购物车记录ID"),
     *                          @OA\Property(property="gid",type="string",description="商品ID"),
     *                          @OA\Property(property="sid",type="string",description="规格ID"),
     *                          @OA\Property(property="num",type="string",description="商品数量"),
     *                          @OA\Property(property="ctime",type="string",description="添加时间"),
     *                          @OA\Property(property="goods_name",type="string",description="商品名称"),
     *                          @OA\Property(property="goods_image",type="string",description="商品图片"),
     *                          @OA\Property(property="goods_price",type="string",description="商品价格"),
     *                          @OA\Property(property="goods_mprice",type="string",description="商品市场价"),
     *                          @OA\Property(property="goods_stock",type="integer",description="商品库存"),
     *                          @OA\Property(property="goods_status",type="string",description="商品状态"),
     *                          @OA\Property(property="limit_num",type="string",description="限购数量"),
     *                          @OA\Property(property="spec_info",type="array",description="规格信息", @OA\Items(type="object"))
     *                      )
     *                  ),
     *                  @OA\Property(property="count",type="integer",description="购物车商品总数量"),
     *                  @OA\Property(property="page",type="integer",description="当前页码"),
     *                  @OA\Property(property="page_size",type="integer",description="每页数量"),
     *                  @OA\Property(property="total_page",type="integer",description="总页数")
     *              )
     *         )
     *     )
     * )
     */
    public function actionList()
    {
        // 验证用户ID
        $userId = $this->user_id;
        if (empty($userId) || strlen($userId) > 11) {
            CUtil::json_response(-1, '请授权登录后再来操作！');
        }

        // 获取分页参数
        $post = \Yii::$app->request->post();
        $page = max(1, intval($post['page'] ?? 1));
        $pageSize = max(1, min(100, intval($post['page_size'] ?? 20)));

        // 获取购物车列表
        list($status, $result) = WaresCartService::getInstance()->getCartList($userId, $this->platformIds, $page, $pageSize);

        // 返回结果
        CUtil::json_response(
            $status ? 1 : -1,
            $status ? 'OK' : $result,
            $status ? $result : []
        );
    }

}
