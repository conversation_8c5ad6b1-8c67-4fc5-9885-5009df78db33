<?php
/**
 * Created by IntelliJ IDEA.
 * User: Kevin
 * Date: 2021/4/30
 * Time: 18:29
 */
namespace app\modules\main\controllers;

use app\components\AdvAscribe;
use app\components\AliYunOss;
use app\components\MemberCenter;
use app\models\by;
use app\models\byNew;
use app\models\CUtil;
use app\modules\back\services\ActivityAtmosphereService;
use app\modules\goods\models\ActivityAtmosphere\ActivityAtmosphereModel;
use app\modules\main\enums\activity\ActivityConfigEnum;
use app\modules\main\formatters\Format;
use app\modules\main\formatters\goods\InfoFormatter;
use app\modules\main\formatters\goods\ListFormatter;
use app\modules\main\formatters\goods\PartFormatter;
use app\modules\main\services\CateService;
use app\modules\main\services\GmainService;
use app\modules\main\services\GoodsPlatformService;
use app\modules\main\services\GoodsService;
use app\modules\main\services\GtiedSaleService;
use app\modules\main\services\TradeInService;
use app\modules\wares\services\goods\GoodsMainService;
use app\modules\wares\services\goods\GoodsStockService;


use RedisException;
use Yii;
use yii\db\Exception;

class GoodsController extends CommController {

    protected $ip;
    protected $page = 1;
    protected $pageSize = 10;

    protected      $format;
    private static $initializedFormatters = null;

    protected static function getFormatters(): ?array
    {
        if (self::$initializedFormatters === null) {
            self::$initializedFormatters = [
                'info' => new InfoFormatter(),
                'list' => new ListFormatter(),
                'part' => new PartFormatter(),
            ];
        }
        return self::$initializedFormatters;
    }

    public function beforeAction($action): bool
    {
        $name = $action->id;

        $formatters = self::getFormatters();
        if (array_key_exists($name, $formatters)) {
            $this->format = new Format($formatters[$name]);
        }

        return parent::beforeAction($action);
    }
    /**
     * 商品标签列表
     */
    public function actionTagList()
    {
        $list = by::model('GtagModel', 'goods')->GetTagCnfList();

        foreach ($list as $key => $li) {
            if ($li['tid']) {
                $detailData = [
                        'platformIds'          => [$this->platformId],                // 平台
                        'is_trade_in'          => $post['is_trade_in'] ?? 0,          // 以旧换新
                        'is_internal_purchase' => $post['is_internal_purchase'] ?? 0, // 内购商城
                ];
                $count      = by::Gmain()->GetListCount($this->version, 0, 0, '', '', $li['tid'], $detailData);
                if (empty($count)) {
                    unset($list[$key]);
                }
            }
        }
        $list = array_values($list);

        CUtil::json_response(1, "OK", $list);

    }


    /**
     * 商品标签列表
     */
    public function actionInternalTagList()
    {
        $post                = Yii::$app->request->post();
        $post['get_channel'] = $this->getChannel;
        $post['sprice_type'] = $this->spriceType;
        $status     = 0;
        $type       = 0;
        $detailData = [
            'platformIds'=>$this->platformIds,
            'is_internal_purchase' => $this->isInternalPurchase,
        ];
        $list   = by::model('GtagModel', 'goods')->GetTagCnfList();
        foreach ($list as $key=>$li){
            if($li['tid']){
                $count                 = by::Gmain()->GetListCount($this->version, $type, $status,'','',$li['tid'],$detailData);
                if(empty($count)){
                    unset($list[$key]);
                }
            }
        }
        $list =array_values($list);
        CUtil::json_response(1,"OK",$list);
    }

    /**
     * @throws \yii\db\Exception
     * @throws \RedisException
     * 查询指定商品详情
     */
    public function actionInfo()
    {
        $gid                 = Yii::$app->request->post("gid", 0);
        $gid = intval($gid);
        $post                = Yii::$app->request->post();
        $single              = $post['single'] ?? 0;
        $is_check_status     = $post['is_check_status'] ?? 0;
        $post['get_channel'] = $this->getChannel;
        $post['sprice_type'] = $this->spriceType;
        $post['user_id']     = $this->user_id;

        if (empty($gid)) {
            CUtil::json_response(-1, "商品不存在或已下架");
        }

        // 校验平台
        $is_check_platform   = $post['is_check_platform'] ?? true;
        if ($is_check_platform) {
            $status = GoodsPlatformService::getInstance()->checkPlatform([$gid], $this->platformId);
            if (!$status) {
                CUtil::json_response(-1, "暂不支持当前平台查看");
            }
        }

        // 校验内购商品
        list($status, $res) = GoodsService::getInstance()->checkInternalPurchase($this->user_id, $gid);
        if (!$status) {
            CUtil::json_response(-1, $res);
        }

        list($status, $aData) = by::Gmain()->SafeInfo($gid, false, $post);
        if (empty($single) && !empty($aData['images'])) {
            $images          = explode('|', $aData['images']);
            $images          = array_map(function ($image) {
                return AliYunOss::factory()->corpPicture($image, 750, 750);
            }, $images);
            $aData['images'] = implode('|', $images);

            !empty($aData['cover_image']) && $aData['cover_image'] = AliYunOss::factory()->corpPicture($aData['cover_image'], 750, 750);
            !empty($aData['market_image']) && $aData['market_image'] = AliYunOss::factory()->corpPicture($aData['market_image'], 750, 750);
            !empty($aData['cover_image_3d']) && $aData['cover_image_3d'] = AliYunOss::factory()->corpPicture($aData['cover_image_3d'], 750, 750);
        }


        if (!$status) {
            CUtil::json_response(-1, $aData, ['good_redirect' => 1]);
        }

        //版本号比较
        if ((CUtil::versionCompare($aData['version'], by::Gmain()::ALL_VERSION) != 0) && (CUtil::versionCompare($this->version, $aData['version']) != 0)) {
            CUtil::json_response(-1, "商品不存在或已下架", ['good_redirect' => 1]);
        }

        // 非游客：处理用户的券后价和商品可用积分
        if ($this->user_id && strlen($this->user_id) < 11) {
            // 构造 `gcombines` 数据并进行 JSON 编码
            $arr['gcombines'] = json_encode([
                    [
                            'gid'     => $aData['gid'] ?? 0,
                            'sid'     => 0,
                            'num'     => 1,
                            'gini_id' => 0,
                    ]
            ]);

            // 调用 BuyInfo 方法以获取价格和折扣详情
            list($status, $result) = by::Ouser()->BuyInfo(intval($this->user_id), $arr);
            if ($status) {
                // 提取返回的折扣信息
                $total             = $result['total'] ?? [];
                $aData['discount'] = [
                        'coin'    => $total['coin'] ?? 0,      // 可用积分
                        'coupon'  => $total['coupon'] ?? 0,    // 优惠券折扣
                        'consume' => $total['consume'] ?? 0,   // 消费券折扣
                        'price'   => $total['real_price'] ?? 0 // 实际支付价格
                ];
            }
        }


        //库存查询-所有sku无库存需展示暂无库存
        $goodsMainService = GoodsMainService::getInstance();
        $source = by::GoodsStockModel()::SOURCE['MAIN'];
        list($stock, $sales) = $goodsMainService->GetSumByGid($gid,$source);
        $aData['stock'] = $stock;
        $aData['sales'] = $sales;
        // 商品限购新标签
        $aData['goods_limit_num'] = $aData['limit_num'] ?? 0;

        // 是否为以旧换新
        $aData['is_trade_in'] = TradeInService::getInstance()->isTradeInProduct($gid, $aData['is_presale'] ?? 0);

        // 免息期数
        $aData['interest_free'] = !empty($aData['jd_baitiao_support']) ? byNew::PaymentActivityModel()->getBaiTiaoInst([$gid]) : 0;

        $aData['is_free_shipping'] =$aData['is_free_shipping']?? 0;

        // 气氛图片
        $aData['atmosphere_img']     = ActivityAtmosphereService::getInstance()->getUrlByGid($gid,ActivityAtmosphereModel::POS['INFO']);

        // 格式化返回数据，此方法不查库，只做数据格式化
        $aData = $this->format->format($aData, $this->platformId);

        CUtil::json_response(1, "OK", [
            'goods' => $aData,
        ]);
    }

    /**
     * 优惠券列表
     * @return void
     * @throws Exception
     * @throws RedisException
     */
    public function actionMarketList()
    {
        $gid = Yii::$app->request->post('gid', 0);
        $gid = intval($gid);
        $userId = $this->user_id;

        // 非会员，不展示优惠券
        if ($gid <= 0 || strlen($userId) > 11 || empty(CUtil::uint($userId))) {
            CUtil::json_response(1, 'ok', []);
        }

        // 获取用户等级
        list($status, $basicInfo) = MemberCenter::factory()->run('basicInfo', ['user_id' => $this->user_id]);
        $level = $basicInfo['currentLevelInfo']['level']['level'] ?? '';

        if (empty($level)) {
            CUtil::json_response(1, 'ok', []);
        }

        $res = GoodsService::getInstance()->getMarketList($userId, $gid, $level);
        CUtil::json_response(1, 'ok', $res);
    }

    /**
     * 小程序商品展示逻辑
     *   - 展示信息为：品类、商品图片、商品名称、商品原价、商品到手价
     * @throws \yii\db\Exception
     */
    public function actionList() {
        $post       = Yii::$app->request->post();
        $post['get_channel'] = $this->getChannel;
        $post['sprice_type'] = $this->spriceType;
        $page       = $post['page'] ?? 1;
        $tid        = intval($post['tid']  ?? -1);
        $page_size  = $post['page_size'] ?? 10;
        $status     = 0;
        $return     = [];
        $type       = 0;
        $detailData = [
          'platformIds'=>[$this->platformId], // 平台
          'is_trade_in'=>$post['is_trade_in'] ?? 0, // 以旧换新
          'is_internal_purchase'=>$post['is_internal_purchase'] ?? 0, // 内购商城
        ];

        // 排序
        $sort = [];
        if (!empty($post['sort_field']) && !empty($post['sort_type'])) {
            $sort = ['sort_field' => $post['sort_field'], 'sort_type' => $post['sort_type']];
        }

        $gids       = by::Gmain()->GetList($page, $page_size, $this->version, $type, $status, '', '', $tid,$detailData,$sort);
        foreach ($gids as $gid) {
            $aGoodsMain = by::Gmain()->GetAllOneByGid($gid, true, true, $post['sprice_type'] ?? 0, false);
            //t_gmain中sku
            $sku    = $aGoodsMain['sku'] ?? '';
            $aGoods = by::Gmain()->GetSpecsPriceInfo($aGoodsMain, $post['sprice_type'] ?? 0);

            $aGoods           = [
                    'gid'                  => $aGoods['gid'],
                    'sku'                  => $sku,
                    'name'                 => $aGoods['name'],
                    'cover_image'          => $aGoods['cover_image'],
                    'market_image'         => empty($aGoods['market_image'] ?? '') ? $aGoods['cover_image'] : $aGoods['market_image'] ?? '',
                    'pc_cover_image'       => $aGoods['pc_cover_image'],
                    'pc_images'            => $aGoods['pc_images'],
                    'mprice'               => $aGoodsMain['mprice'],
                    'price'                => $aGoodsMain['price'], //区分多规格商品 统一取优惠价格
                    'is_presale'           => $aGoods['is_presale'] ?? 0,
                    'presale_time'         => $aGoods['presale_time'] ?? 0,
                    'is_internal'          => $aGoods['is_internal'] ?? 0,
                    'is_internal_purchase' => $aGoods['is_internal_purchase'] ?? 0,
                    'is_ini'               => $aGoods['is_ini'] ?? 0,
                    'gini_id'              => $aGoods['gini_id'] ?? 0,
                    'is_trade_in'          => $aGoods['is_trade_in'] ?? 0,
                    'tids'                 => $aGoods['tids'] ?? [],
                    'custom_tag'           => $aGoods['custom_tag'] ?? '',
                    'deposit'              => $aGoods['deposit'] ?? '',
                    'expand_price'         => $aGoods['expand_price'] ?? '',
                    'atmosphere_img'       => ActivityAtmosphereService::getInstance()->getUrlByGid($aGoods['gid'],ActivityAtmosphereModel::POS['LIST']),
                    'coupon_price'         => $aGoodsMain['price'], // 券后价 不计算 给个默认值
            ];
            $return['list'][] = $aGoods;
        }

        // 旧版本要兼容  根据前端传特殊字段single来区分
        if ($this->user_id && strlen($this->user_id) < 11 && !empty($return['list']) && !isset($post['single'])) {
            foreach ($return['list'] as &$item) {
                $goodsCombine = [[
                        'gid'     => $item['gid'] ?? 0,
                        'sid'     => 0,
                        'num'     => 1,
                        'gini_id' => 0,
                ]];
                // 如果是多规格价格取的是规格中的某一个价格 但是优惠价格是根据 price计算的
                $couponData = by::Ouser()->applyCouponDiscount(intval($this->user_id), $goodsCombine);
                // 原价减去优惠券抵扣价
                $item['coupon_price'] = bcsub($item['price'], $couponData['discount_price'], 2);
            }
        }

        $count                 = by::Gmain()->GetListCount($this->version, $type, $status,'','',$tid,$detailData);
        $return['pages']       = CUtil::getPaginationPages($count, $page_size);
        $return['total']       = $count;

        //推送腾讯
        if($this->union && stristr($this->union,'tencent')){
            AdvAscribe::factory()->push($this->user_id,'tencent',['user_id'=>$this->user_id,'url'=>$this->referer,'event'=>'VIEW_CONTENT','click_id'=>$this->clickId,'extra'=>['object'=>'PRODUCT']]);
        }

        // 格式化返回数据，此方法不查库，只做数据格式化
        $return = $this->format->format($return, $this->platformId);

        CUtil::json_response(1, 'ok', $return);
    }


    /**
     * @OA\Post(
     *     path="/main/goods/calculate-price",
     *     summary="商品-计算券后价格",
     *     description="计算指定商品组合的券后价格，包括优惠信息和最终价格。",
     *     tags={"商品"},
     *     security={
     *         {"userid": {}},
     *         {"sessid": {}}
     *     },
     *     @OA\RequestBody(
     *         required=true,
     *         @OA\MediaType(
     *             mediaType="application/x-www-form-urlencoded",
     *             @OA\Schema(
     *                 type="object",
     *                 @OA\Property(
     *                     property="goods_combines",
     *                     type="string",
     *                     default="",
     *                     description="商品组合，JSON 字符串格式，包含以下字段：<br>
     *                     - gid：商品 ID<br>
     *                     - sid：多规格 ID<br>
     *                     - num：数量，默认传 1<br>
     *                     - gini_id：默认传 0<br>
     *                     - price：商品单价"
     *                 )
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response="200",
     *         description="返回计算后的券后价格信息。",
     *         @OA\JsonContent(
     *             required={"iRet", "sMsg", "data"},
     *             @OA\Property(property="iRet", type="integer", description="状态码，例如 1 表示成功。"),
     *             @OA\Property(property="sMsg", type="string", description="提示消息，例如 'ok'。"),
     *             @OA\Property(
     *                 property="data",
     *                 type="array",
     *                 description="计算结果数据",
     *                 @OA\Items(
     *                     @OA\Property(property="gid", type="integer", description="商品 ID"),
     *                     @OA\Property(property="sid", type="integer", description="多规格 ID"),
     *                     @OA\Property(property="num", type="integer", description="商品数量"),
     *                     @OA\Property(property="price", type="number", format="float", description="商品原价"),
     *                     @OA\Property(property="discount_price", type="number", format="float", description="优惠金额"),
     *                     @OA\Property(property="coupon_price", type="number", format="float", description="券后价格")
     *                 )
     *             )
     *         )
     *     )
     * )
     */
    public function actionCalculatePrice()
    {
        $goodsCombinesJson = Yii::$app->request->post('goods_combines', '');
        $goodsCombinesData = json_decode($goodsCombinesJson, true);

        // 确定是否为用户模式
        $isUserMode = !empty($this->user_id) && ctype_digit($this->user_id) && strlen($this->user_id) < 11;

        // 如果有商品组合数据
        if (!empty($goodsCombinesData)) {
            foreach ($goodsCombinesData as &$item) {
                // 初始化优惠价格为商品原价
                $item['coupon_price']   = $item['price'];
                $item['discount_price'] = 0;

                if ($isUserMode) {
                    // 用户模式：计算优惠券折扣
                    $goodsCombine = [$item];
                    $couponData   = by::Ouser()->applyCouponDiscount((int) $this->user_id, $goodsCombine);

                    // 更新优惠价格和折扣价格
                    $item['discount_price'] = $couponData['discount_price'] ?? 0;
                    $item['coupon_price']   = bcsub($item['price'], $item['discount_price'], 2);
                }
            }
        }

        // 返回响应
        CUtil::json_response(1, 'ok', $goodsCombinesData);
    }




    /**
     * 小程序商品展示逻辑
     *   - 展示信息为：品类、商品图片、商品名称、商品原价、商品到手价
     * @throws \yii\db\Exception
     */
    public function actionInternalList() {
        $post       = Yii::$app->request->post();
        $post['get_channel'] = $this->getChannel;
        $post['sprice_type'] = $this->spriceType;
        $page       = $post['page'] ?? 1;
        $tid        = $post['tid']  ?? -1;
        $page_size  = $post['page_size'] ?? 25;
        $status     = 0;
        $return     = [];
        $type       = 0;
        $detailData = [
            'platformIds'=>$this->platformIds,
            'is_internal_purchase' => $this->isInternalPurchase,
        ];

        $gids       = by::Gmain()->GetList($page, $page_size, $this->version, $type, $status, '', '', $tid,$detailData);
        foreach ($gids as $gid) {
            $aGoods = by::Gmain()->GetAllOneByGid($gid,true,true,$post['sprice_type'] ?? 0);
            $aGoods = by::Gmain()->GetSpecsPriceInfo($aGoods,$post['sprice_type'] ?? 0);

            $aGoods = [
                'gid'                  => $aGoods['gid'],
                'name'                 => $aGoods['name'],
                'cover_image'          => $aGoods['cover_image'],
                'market_image'         => empty($aGoods['market_image'] ?? '') ? $aGoods['cover_image'] : $aGoods['market_image'] ?? '',
                'mprice'               => $aGoods['mprice'],
                'price'                => $aGoods['price'],
                'uprice'               => $aGoods['price'],
                'tids'                 => $aGoods['tids'],
                'is_internal'          => $aGoods['is_internal'] ?? 0,
                'is_internal_purchase' => $aGoods['is_internal_purchase'] ?? 0,
                'is_ini'               => $aGoods['is_ini'] ?? 0,
                'gini_id'              => $aGoods['gini_id'] ?? 0,
            ];
            $return['list'][] = $aGoods;
        }

        $count                 = by::Gmain()->GetListCount($this->version, $type, $status,'','',$tid,$detailData);
        $return['pages']       = CUtil::getPaginationPages($count, $page_size);

        CUtil::json_response(1, 'ok', $return);
    }

    /**
     * @throws \yii\db\Exception
     * 属性库存
     */
    public function actionAttrStock()
    {
        $post   = Yii::$app->request->post();
        $gid    = intval($post['gid'] ?? 0);   // 7
        $av_ids = $post['av_ids'] ?? "";       //"[18]"

        if (empty($gid)) {
            CUtil::json_response(-1, '商品id不能为空');
        }
        $return = GoodsStockService::getInstance()->InStock($gid, $av_ids, by::GoodsStockModel()::SOURCE['MAIN']);

        CUtil::json_response(1, 'ok', $return);
    }

    /**
     * @throws \yii\db\Exception
     * 规格查询
     */
    public function actionSpecsInfo() {

        $post                = Yii::$app->request->post();
        $post['get_channel'] = $this->getChannel;
        $post['sprice_type'] = $this->spriceType;
        $gid                 = intval($post['gid'] ?? 0);
        $av_ids              = $post['av_ids'] ?? "";
        $num                 = $post['num'] ?? 1;

        if (empty($gid)) {
            CUtil::json_response(-1, '商品id不能为空');
        }

        list($s, $aData) = by::Gspecs()->GetOneByAvIds($gid,$av_ids,$num,$post);
        if(!$s){
            CUtil::json_response(-1, $aData);
        }

        if ($this->user_id && strlen($this->user_id) < 11) {
            // 构造 `gcombines` 数据并进行 JSON 编码
            $arr['gcombines'] = json_encode([
                    [
                            'gid'     => $aData['gid'] ?? 0,
                            'sid'     => $aData['id'] ?? 0,
                            'num'     => 1,
                            'gini_id' => 0,
                    ]
            ]);

            // 调用 BuyInfo 方法以获取价格和折扣详情
            list($status, $result) = by::Ouser()->BuyInfo(intval($this->user_id), $arr);
            if ($status) {
                // 提取返回的折扣信息
                $total             = $result['total'] ?? [];
                $aData['discount'] = [
                        'coin'    => $total['coin'] ?? 0,      // 可用积分
                        'coupon'  => $total['coupon'] ?? 0,    // 优惠券折扣
                        'consume' => $total['consume'] ?? 0,   // 消费券折扣
                        'price'   => $total['real_price'] ?? 0 // 实际支付价格
                ];
            }
        }


        CUtil::json_response(1, 'ok', $aData);
    }

    /**
     * 商品规格列表
     * @return void
     */
    public function actionSpecsList()
    {
        $post = Yii::$app->request->post();
        $gid = $post['gid'] ?? 0;

        if (!$gid) {
            CUtil::json_response(-1, '商品id不能为空');
        }

        // 获取多规格数据
        try {
            $res = GoodsService::getInstance()->getSpecList($gid, $this->spriceType);
            CUtil::json_response(1, 'ok', $res);
        } catch (\Exception $e) {
            CUtil::json_response(-1, '商品信息获取失败');
        }
    }

    /**
     * @throws \yii\db\Exception
     * 规程查属性
     */
    public function actionAttrInfo() {
        $post       = Yii::$app->request->post();
        $gid        = $post['gid']      ?? 0;

        list($s, $aData) = by::Gspecs()->GetAttrByGid($gid);
        if(!$s){
            CUtil::json_response(-1, $aData);
        }

        CUtil::json_response(1, 'ok', $aData);
    }

    /**
     * @throws \yii\db\Exception
     * 分享海报二维码
     */
    public function actionShareCode()
    {
        $post       = Yii::$app->request->post();
        $gid        = $post['gid'];
        $code       = $post['code'] ?? "";

        list($s, $ret) = by::Gmain()->ShareCode($gid,$code);
        if (!$s) {
            CUtil::json_response(-1, $ret);
        }

        CUtil::json_response(1, 'ok', $ret);
    }


    /**
     * 内购分销二维码
     * @return void
     * @throws \yii\db\Exception
     */

    public function actionInternalShareCode()
    {
        $post       = Yii::$app->request->post();
        $code       = $post['code'] ?? "";

        list($s, $ret) = by::Gmain()->InternalShareCode($code);
        if (!$s) {
            CUtil::json_response(-1, $ret);
        }

        CUtil::json_response(1, 'ok', $ret);
    }


    /**
     * @throws \yii\db\Exception
     * 获取导购员加密串-导购小程序用
     */
    public function actionScode()
    {
        $source = Yii::$app->request->post('source', 0);

        list($s, $ret) = by::Gmain()->Scode($this->user_id, $source);
        if (!$s) {
            CUtil::json_response(-1, $ret);
        }

        CUtil::json_response(1, 'ok', $ret);
    }


    /**
     * 好物推荐列表
     * @return void
     * @throws \yii\db\Exception
     */
    public function actionRecommend()
    {
        $post                = Yii::$app->request->post();
        $is_recommend        = $post['is_recommend'] ?? 1;
        $page_size           = $post['page_size'] ?? 200;
        $post['sprice_type'] = $this->spriceType;
        $post['status']      = 0;
        $post['type']        = 0;
        $detailData = [
            'platformIds'=>$this->platformIds,
            'is_internal_purchase' => $this->isInternalPurchase,
        ];
        $detailData['is_recommend'] = $is_recommend;
        $return                     = [];
        $this->ip                   = Yii::$app->request->getUserIP();
        list($status, $data) = by::Gmain()->getRecommendList($this->ip, $this->user_id, $post, $detailData, $this->version);
        if (!$status) {
            CUtil::json_response(-1, $data);
        }
        $return['list']  = $data['list'] ?? [];
        $return['count'] = $data['count'] ?? 0;
        $return['pages'] = CUtil::getPaginationPages($return['count'], $page_size);

        CUtil::json_response(1, 'ok', $return);
    }


    /**
     * 获取配件类目
     */
    public function actionPartCate()
    {
        $type = \Yii::$app->request->post('type', 2);
        list($status, $ret) = CateService::getInstance()->getList($type,true,true,$this->platformId);
        if (!$status) {
            CUtil::json_response(-1, $ret);
        }

        CUtil::json_response(1, 'ok', $ret);
    }

    /**
     * @throws Exception
     * @throws RedisException
     * 获取配件数据
     */
    public function actionPart()
    {
        $c_id = \Yii::$app->request->post('c_id', 0);
        $id   = \Yii::$app->request->post('id', '');
        $ids  = array_filter(explode(',', $id));
        $sort = \Yii::$app->request->post('sort', 3);
        $is_internal_purchase = \Yii::$app->request->post('is_internal_purchase', 0);

        list($status, $ret) = CateService::getInstance()->getPartList($c_id, $ids, $sort, $this->platformId, $is_internal_purchase);
        if (!$status) {
            CUtil::json_response(-1, $ret);
        }

        // 获取不显示的商品ID
        $hiddenGids = ActivityConfigEnum::getHiddenGoods();
        // 过滤掉不显示的商品
        $ret = array_filter($ret, function ($item) use ($hiddenGids) {
            return !in_array($item['id'], $hiddenGids);
        });


        // 处理当前平台数据
        $ret = $this->format->format(array_values($ret), $this->platformId);

        CUtil::json_response(1, 'ok', $ret);
    }

    /**
     * @throws Exception
     * @throws RedisException
     * 筛选数据
     */
    public function actionChoose()
    {
        $c_id = Yii::$app->request->post('c_id', 0);

        // 调用 choose 方法并检查返回状态
        list($status, $data) = by::gCateModel()->choose($c_id, $this->platformId);

        // 根据状态返回相应的 JSON 响应
        if (!$status) {
            CUtil::json_response(-1, '获取失败');
        }

        CUtil::json_response(1, 'ok', $data);
    }


    /**
     * @throws Exception
     * @throws RedisException
     * 搭配购买
     */
    public function actionTiedSale()
    {
        $sku = Yii::$app->request->post('sku', '');

        if (empty($sku)) {
            CUtil::json_response(0, 'SKU不能为空');
        }

        list($status, $ret) = GtiedSaleService::getInstance()->getTiedSaleInfo($sku, $this->platformId);

        if (!$status) {
            CUtil::json_response(-1, '获取失败');
        }

        CUtil::json_response(1, 'ok', $ret);
    }


    /**
     * 商品上下架状态
     * @throws Exception
     */
    public function actionSaleStatus()
    {
        $sku = \Yii::$app->request->post('sku');
        if (empty($sku)) {
            CUtil::json_response(-1, '商品参数错误');
        }
        $status = GmainService::getInstance()->getSaleStatus($sku);
        CUtil::json_response(1, 'ok', ['status' => $status]);
    }


    /**
     * 获取商品标签
     * @return void
     * @throws Exception
     */
    public function actionTags()
    {
        $gid = \Yii::$app->request->post('gid');
        $gid = intval($gid);
        if (empty($gid)) {
            CUtil::json_response(-1, '商品参数错误');
        }
        // 获取商品名称
        $name = GmainService::getInstance()->getGoodsNameByGid($gid);
        // 获取商品标签
        $tags = GmainService::getInstance()->getTagsByGid($gid);
        CUtil::json_response(1, 'ok', ['product_name' => $name, 'tids_name' => $tags]);
    }

    /**
     * 内购商城banner
     * @return void
     */
    public function actionInternalBanner()
    {
        $banners = file_get_contents(__DIR__ . '/../../main/enums/internalmall/banner.json');
        $banners = json_decode($banners, true);
        // 过滤掉时间范围不是当前时间的banner
        $banners = array_filter($banners, function ($banner) {
            $start = strtotime($banner['start_time']);
            $end   = strtotime($banner['end_time']);
            $now   = time();
            return $now >= $start && $now <= $end;
        });

        CUtil::json_response(1, 'ok', $banners);
    }
}
