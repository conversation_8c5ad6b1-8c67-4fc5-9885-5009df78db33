<?php
/**
 * Created by IntelliJ IDEA.
 * User: Kevin
 * Date: 2021/6/4
 * Time: 14:45
 * 商品主表
 */
namespace app\modules\goods\models;

use app\components\AppCRedisKeys;
use app\components\Erp;
use app\components\ErpNew;
use app\jobs\ProductSaveJob;
use app\models\by;
use app\models\byNew;
use app\models\CUtil;
use app\models\MyExceptionModel;
use app\models\Response;
use app\modules\main\controllers\CommController;
use app\modules\main\models\CommModel;
use app\modules\main\models\GcateModel;
use app\modules\main\services\GroupPurchaseService;
use app\modules\main\services\TradeInService;
use app\modules\wares\services\goods\GoodsMainService;
use RedisException;
use yii\db\DataReader;
use yii\db\Exception;


class GmainModel extends CommModel {

    CONST SHARE_KEY     = "SHARE_UP";
    CONST MAX_VERSION   = "999.999.999";
    CONST ALL_VERSION   = "0.0.0";

    const TYPE = [
        'COMMON' => 0,      //普通商品
        'Y_LINK' => 99,     //一元链接
    ];

    const PTYPE = [
        'NO_PRESALE'  => 0, //不参与预售
        'PRESALE'     => 1, //参与过预售（包括定金订单和尾款订单）
    ];

    const STATUS = [
        'ON_SALE' => 0,     //在售
        'OFF_SALE' => 1,    //下架
    ];

    const ACT = [
        'UP'      => 1010,
        'DOWN'    => 1020,
    ];

    const CODE_SOURCE = [
        'GUIDE'    => 1,
        'REFERRER' => 2,
    ];

    const SPRICE_TYPE = [
        'INTERNAL' =>   1,//内购
    ];

    public $tb_fields = [
        'id','sku','short_code','name','compare_sku','is_compare','type','status','sort','is_del','version','ck_code','ctime'
    ];


    /**
     * @return string
     */
    public static function tableName(): string
    {
        return self::tbName();
    }

    public static function tbName(): string
    {
        return  "`db_dreame_goods`.`t_gmain`";
    }

    /**
     * @param $gid
     * @return string
     * 商品唯一数据缓存KEY
     */
    private function __getOneGmianKey($gid): string
    {
        return AppCRedisKeys::getOneGmainByGid($gid);
    }

    /**
     * @param $sku
     * @return string
     * 商品sku数据缓存KEY
     */
    private function __getOneGmianBySkuKey($sku,$is_del): string
    {
        return AppCRedisKeys::getOneGmainBySku($sku,$is_del);
    }

    /**
     * @return string
     * 商品哈希列表
     */
    private function __getGoodsListKey() {
        return AppCRedisKeys::getGoodsList();
    }

    /**
     * @return string
     * 商品不多，因此缓存所有的商品基础信息
     */
    private function __getAllGoodsListKey() {
        return AppCRedisKeys::getAllGoodsList();
    }

    /**
     * @param $gid
     * @return int
     * 缓存清理
     * @throws Exception
     */
    private function __delCache($gid): int
    {
        $r_key1 = $this->__getOneGmianKey($gid);

        return  by::redis('core')->del($r_key1);
    }

    /**
     * @param $gid
     * @return int
     * sku缓存清理
     * @throws Exception
     */
    private function __delSkuCache($sku): int
    {
        $r_key1 = $this->__getOneGmianBySkuKey($sku,0);
        $r_key2 = $this->__getOneGmianBySkuKey($sku,1);

        return  by::redis('core')->del($r_key1,$r_key2);
    }

    /**
     * @return int
     * 清理缓存列表(每种类型都要清理)
     */
    private function __delListCache(): int
    {
        $r_key = $this->__getGoodsListKey();
        $r_all_key = $this->__getAllGoodsListKey();
        return by::redis('core')->del($r_key,$r_all_key);
    }

    /**
     * @param array $aData
     * @return array
     * 校验主表参数
     */
    private function __check(array $aData)
    {
        $sku         = $aData['sku'] ?? "";
        $name        = $aData['name'] ?? "";
        $ck_code     = $aData['ck_code'] ?? "";
        $type        = $aData['type'] ?? 0;
        $is_compare  = $aData['is_compare'] ?? 0;
        $compare_sku = $aData['compare_sku'] ?? '';

        if (empty($name)) {
            return [false, "商品名称不能为空"];
        }

        if (empty($sku)) {
            return [false, "商品编码不能为空"];
        }

        if ($type < self::TYPE['Y_LINK']) {
            if (empty($ck_code) || !in_array($ck_code, Erp::CK_CODE)) {
                return [false, "请选择正确的仓库"];
            }
        }

        if ($is_compare && empty($compare_sku)) {
            return [false, "对比商品不能为空"];
        }

        // PC平台不支持内购商品
        if ($aData['is_internal_purchase'] == 1 && strpos($aData['platform_ids'], '3') !== false) {
            return [false, "PC平台不支持内购商品"];
        }

        return [true, 'ok'];
    }

    public function getType0()
    {
        return $this->hasOne(Gtype0Model::class, ['gid' => 'id']);
    }

    /**
     * @param array $aData
     * @return array
     * @throws Exception
     * 商品主表增改
     */
    public function SaveLog(array $aData): array
    {
        //校验主表参数
        list($s, $m) = $this->__check($aData);
        if (!$s) {
            return [false, $m];
        }

        $sku        = $aData['sku']     ?? "";
        $short_code = $aData['short_code']     ?? "";
        $name       = $aData['name']    ?? "";
        $ck_code    = $aData['ck_code'] ?? "";
        $compare_sku= $aData['compare_sku'] ?? "";
        $is_compare = $aData['is_compare'] ?? 0;

        $id         = $aData['id']          ?? 0;
        $type       = $aData['type']        ?? 0;
        $sort       = $aData['sort']        ?? 0;
        $status     = $aData['status']      ?? 1;
        $version    = $aData['g_version']   ?? "";//商品版本号
        $c_id       = $aData['c_id']        ?? 0;
        $part_skus  = $aData['part_skus']   ?? '';

        $tied_sale  = $aData['tied_sale']   ?? '';
        $tied_sale  = json_encode(CUtil::uniqueMultidimArray(json_decode($tied_sale,true),'part_sku'),320);

        $params     = $aData['params']   ?? ''; //商品参数

        $part_skus  = implode(',',array_unique(array_filter(explode(',',$part_skus))));


        $id         = CUtil::uint($id);
        $type       = CUtil::uint($type);
        $sort       = CUtil::uint($sort);
        $status     = CUtil::uint($status);
        $c_id       = CUtil::uint($c_id);
        $version    = CUtil::version2long($version);
        $sku        = trim($sku);

        $save = [
            'sku'       => $sku,
            'short_code'=> $short_code,
            'name'      => $name,
            'compare_sku'=> $compare_sku,
            'is_compare'=> $is_compare,
            'type'      => $type,
            'status'    => $status,
            'sort'      => $sort,
            'version'   => intval($version),
            'ck_code'   => $ck_code,
        ];

        if ($id) {
            $aGoods = $this->GetOneByGid($id);
            if (empty($aGoods)) {
                return [false, '参数错误'];
            }

            unset($save['sku'], $save['type']); //不能修改的参数

        } else {
            //新增
            $aGoods = $this->GetOneBySku($sku);
            if(!empty($aGoods)) {
                return [false,"商品编码对应商品已存在"];
            }

            $save['ctime'] = intval(START_TIME);
        }

        $db     = by::dbMaster();
        $tb     = $this->tbName();
        $trans  = $db->beginTransaction();

        try {
            if ($id) {
                $db->createCommand()->update($tb,$save,"`id`=:id",[":id"=>$id])->execute();
                $this->__delCache($id); //todo 清空详情缓存

            } else {
                $db->createCommand()->insert($tb,$save)->execute();
                $id = $db->getLastInsertID();

                $this->__delSkuCache($sku);
            }

            switch ($type) {
                case self::TYPE['COMMON'] :
                    list($s, $m) = by::Gtype0()->SaveLog($id, $aData);
                    break;

                case self::TYPE['Y_LINK'] :
                    list($s, $m) = by::Gtype99()->SaveLog($id, $aData);
                    break;

                default :
                    list($s, $m) = [false, '类型错误'];
            }

            if (!$s) {
                throw new MyExceptionModel($m);
            }

            //处理缓存数据
            $mainIds = by::partsSalesModel()->getMainIdBySku($sku,by::partsSalesModel()::PART_TYPE);

            $cate = by::gCateModel()->getCateInfoBySku($sku);
            $before_cid = $cate['c_id'] ?? '';
            $info = by::mainPartModel()->getInfoByMainSku($sku);
            $part_key = $info['part_sku'] ?? '';

            $main_sku = by::mainPartModel()->getMainByPartSku($sku);
            $main_sku = array_column($main_sku,'main_sku');

            //保存关联数据
            list($part_status, $msg) = by::mainPartModel()->saveLog($sku, $part_skus,$tied_sale);
            if (!$part_status) {
                throw new MyExceptionModel($msg);
            }

            //商品绑定分类信息
            if ($c_id) {
                list($c_status, $ret) = by::gCateModel()->saveLog($sku, $c_id,$status);
                if (!$c_status) {
                    throw new MyExceptionModel($ret);
                }
            }

            //保存商品参数
            if ($params) {
                $params = (array)json_decode($params, true);
                list($p_status, $ret) = by::GparamGoodsModel()->saveLog($sku, $c_id, $params);
                if (!$p_status) {
                    throw new MyExceptionModel($ret);
                }
            }

            //产品注册修改配件上下架
            $part_tb = by::partsSalesModel()::tbName();
            by::dbMaster()->createCommand()->update($part_tb,['status'=>$status],['part_sku'=>$sku])->execute();

            $trans->commit();

            //异步触发修改产品tid
            \Yii::$app->queue->push(new ProductSaveJob());

            $this->__delListCache();
            by::gCateModel()->__delCateInfoBySkuCache(explode(',',$sku));
            by::gCateModel()->__delCateInfoByCidCache($c_id);
            by::gCateModel()->__delCateInfoByCidCache($before_cid);
            by::Gtag()->__delGtagCache();
            by::partsSalesModel()->__delHostAndPartCache();
            by::mainPartModel()->__delGetMainByPartSku(explode(',',$sku));
            by::mainPartModel()->__delGetMainByPartSku(explode(',',$part_key));
            by::cateModel()->__delGetListCache();
            by::mainPartModel()->__delGetInfoByMainSku($main_sku);
            by::partsSalesModel()->__delGetOneCache($mainIds);
            by::mainSalesModel()->__delGetMainInfoByIdCache($mainIds);
        } catch (MyExceptionModel $e) {
            $trans->rollBack();
            CUtil::debug($e->getMessage(), 'err.gmain');
            return [false, $e->getMessage()];

        } catch (\Exception $e) {
            $trans->rollBack();

            CUtil::debug($e->getMessage(), 'err.gmain');

            return [false, '操作失败'];
        }

        return [true, $id];
    }

    /**
     * @param $sku
     * @param $is_del
     * @return array|false|DataReader
     * @throws Exception
     * @throws RedisException
     * 根据商品唯一码：SKU，获取商品信息
     */
    public function getOneBySku($sku, $is_del = 0)
    {
        $gids = $this->getOneBySkus([$sku], $is_del); // 注意：$sku 需要是数组
        $gid  = $gids[$sku] ?? 0;
        return $this->getOneByGid($gid);
    }


    /**
     * @param $sku
     * @param $is_del
     * @return array
     * @throws RedisException
     * 优化批量获取方法，sku为数组
     */
    public function GetOneBySkus($sku, $is_del = 0): array
    {
        if (empty($sku) || !is_array($sku)) {
            return [];
        }
        $r_key = $this->__getOneGmianBySkuKey(json_encode($sku), $is_del);

        $redis = by::redis('core');
        $goods = (array)json_decode($redis->get($r_key), true) ?? [];


        if (empty($goods)) {
            if ($is_del) {
                $query = self::find()->where(['sku' => $sku])->orderBy(['ctime' => SORT_DESC]);
            } else {
                $query = self::find()->where(['sku' => $sku, 'is_del' => 0])->orderBy(['ctime' => SORT_DESC]);
            }

            $goods = $query->select(['id', 'sku'])->asArray()->all();

            $redis->set($r_key, json_encode($goods, 320), ['EX' => $goods ? 3600 : 10]);
        }

        return array_column($goods, 'id', 'sku');
    }

    /**
     * @param $gid
     * @return array|false|DataReader
     * @throws Exception
     * 获取指定详情信息
     */
    public function GetOneByGid($gid) {
        $gid = CUtil::uint($gid);
        if($gid <= 0) {
            return [];
        }

        $redis       = by::redis('core');
        $redis_key   = $this->__getOneGmianKey($gid);
        $aJson       = $redis->get($redis_key);
        $aData       = (array)json_decode($aJson,true);
        if($aJson === false) {
            $tb      = $this->tbName();
            $fields  = implode("`,`",$this->tb_fields);
            $sql     = "SELECT `{$fields}` FROM {$tb} WHERE `id`=:id LIMIT 1";
            $aData   = by::dbMaster()->createCommand($sql, [':id' => $gid])->queryOne();
            $aData   = empty($aData) ? [] : $aData;

            $redis->set($redis_key,json_encode($aData),['EX'=>empty($aData) ? 10 : 3600]);
        }

        if (empty($aData)) {
            return [];
        }

        $aData['version']    = CUtil::long2version($aData['version']);

        return $aData;
    }

    /**
     * @param $gid
     * @param bool $base
     * @param bool $s_tid
     * @return array
     * @throws Exception
     * @throws RedisException
     * 获取指定商品所有信息
     */
    public function GetAllOneByGid($gid, $base = true, $s_tid = true, $spriceType=0, $partCate=true)
    {
        $aMain       = $this->GetOneByGid($gid);
        if (empty($aMain)) {
            return [];
        }

        //商品信息
        $type = $aMain['type'] ?? 0;
        switch ($type) {
            case self::TYPE['COMMON'] :
                if ($base) {
                    $aGtype = by::Gtype0()->GetOneBaseByGid($gid, true, $spriceType, $aMain['sku'] ?? '');
                } else {
                    $aGtype = by::Gtype0()->GetOneByGid($gid, $spriceType, $aMain['sku'] ?? '');
                }
                break;
            case self::TYPE['Y_LINK'] :
                if ($base) {
                    $aGtype = by::Gtype99()->GetOneBaseByGid($gid, true, $spriceType, $aMain['sku'] ?? '');
                } else {
                    $aGtype = by::Gtype99()->GetOneByGid($gid, $spriceType, $aMain['sku'] ?? '');
                }
                break;
            default :
                return [];
        }


        if($partCate){ //是否需要分类信息
            //判断类目
            $cate_info = by::gCateModel()->getCateInfoBySku($aMain['sku']) ?? [];

            if (empty($cate_info)){
                $aMain['cate'] = [];
            }else{
                $c_id = $cate_info['c_id'] ?? '';
                $parent = by::cateModel()->findParent($c_id) ?? [];
                $c_ids = array_column($parent,'id');

                $aMain['cate'] = $c_ids ?? [];
            }
            //组装关联信息
            $aMain['part_info'] = by::mainPartModel()->getInfoByMainSku($aMain['sku']);
        }

        // 查询商品标签
        if ($s_tid) {
            $tags = $this->getProductTags($gid);
            $aGtype['tids'] = $tags['tids'];
            $aGtype['tids_name'] = $tags['tids_name'];
        }

        $aData = array_merge($aMain, $aGtype);
        $aData = Response::responseList($aData,[
           'is_presale'     => 'int',
           'presale_time'   => 'time|ms',
           'start_payment'  => 'time|ms',
           'end_payment'    => 'time|ms',
           'surplus_time'   => 'is_int',
           'scheduled_number' => 'int',
        ]);

        return $aData;
    }



    /**
     * @param $aGoods
     * @return mixed
     * @throws Exception
     * 预处理多规格商品价格
     */
    public function GetSpecsPriceInfo($aGoods,$spriceType)
    {
        $atype = $aGoods['atype'] ?? -1;
        $gid   = $aGoods['gid'] ?? 0;
        if(empty($gid)) return $aGoods;
        $aGoods['min_specs'] = [];
        if ($atype == by::Gtype0()::ATYPE['SPECS']) {
            //规格列表
            $specs    = by::Gspecs()->GetListByGid($gid,$spriceType);
            $internalArr = array_column($specs, 'is_internal') ?? [];
            $iniArr = array_column($specs, 'is_ini') ?? [];
            $priceArr = array_column($specs, 'price') ?? [];

            if($internalArr && $iniArr && $priceArr){
                array_multisort($internalArr, SORT_DESC,
                    $iniArr, SORT_DESC,
                    $priceArr, SORT_ASC,
                    $specs);
            }

            $specsPrice            = $specs[0]['price'] ?? 0;
            $specsPcombines        = $specs[0]['pcombines'] ?? [];
            $aGoods['price']       = (floatval($specsPrice) > 0) ? $specsPrice : $aGoods['price'];
            $aGoods['pcombines']   = (!empty($specsPcombines)) ? $specsPcombines : $aGoods['pcombines'];
            $aGoods['sku']         = $specs[0]['sku'] ?? $aGoods['sku'];
            $aGoods['min_specs']   = $specs[0] ?? [];
            $aGoods['is_internal'] = $specs[0]['is_internal'] ?? 0;
            $aGoods['is_ini']      = $specs[0]['is_ini'] ?? 0;
            $aGoods['gini_info']   = $specs[0]['gini_info'] ?? [];
            $aGoods['gini_id']     = $specs[0]['gini_id'] ?? 0;
            $aGoods['gini_etime']  = $specs[0]['gini_etime'] ?? 0;
            $aGoods['gini_tag']    = $specs[0]['gini_tag'] ?? '';
        }
        return $aGoods;
    }


    /**
     *  通过商品ID获取商品标签
     * @var array
     */
    public $GetOneByGidSidCache=[];

    /**
     * @param $gid
     * @param $sid
     * @param $format_price
     * @param $attr_to_name
     * @param $spriceType
     * @return array
     * @throws Exception
     */
    public function GetOneByGidSid($gid,$sid=0, $format_price=true,$attr_to_name=false,$spriceType=0): array
    {
        // 以旧换新计算价格修改
        $is_trade_in = \Yii::$app->params['is_trade_in']??0;

        // 优化重复调用的缓存键（cache key）
        $key=$gid.'_'.$sid.'_'.(int)$format_price.'_'.(int)$attr_to_name.'_'.$spriceType.'_'.$is_trade_in;
        if (isset($this->GetOneByGidSidCache[$key])) {
            return $this->GetOneByGidSidCache[$key];
        }

        $aMain  = $this->GetOneByGid($gid);

        if (empty($aMain)) {
            return [];
        }

        $type       = $aMain['type'] ?? 0;

        switch ($type) {
            case self::TYPE['COMMON'] :
                $aGtype     = by::Gtype0()->GetOneBaseByGid($gid, $format_price, $spriceType, $aMain['sku'] ?? 0);

                $aGtag          = by::model('GtagModel','goods')->GetListByGid($gid);
                $aGtype['tids'] = [];
                if (!empty($aGtag)) {
                    $tids           = array_column($aGtag, 'tid');
                    $aGtype['tids'] = $tids;
                }

                $aGtype['spec'] = [];
                if ($sid > 0) {
                    $spec           = by::Gspecs()->GetOneById($gid, $sid, $format_price,true, $spriceType);

                    if ($attr_to_name) {
                        $av_ids         = $spec['av_ids'] ?? '';
                        $attr_cnf       = [];
                        if ($av_ids) {
                            $av_ids = json_decode($av_ids, true);
                            foreach ($av_ids as $k1 => $v1){
                                $cnf = by::Gav()->IdToName($v1);
                                !empty($cnf) && $attr_cnf[] = $cnf;
                            }

                            $spec['attr_cnf'] = $attr_cnf;
                        }
                    }

                    $aGtype['spec'] = $spec;

                }

                $aData = array_merge($aMain, $aGtype);
                break;

            case self::TYPE['Y_LINK'] :
                $aGtype     = by::Gtype99()->GetOneBaseByGid($gid, $format_price, $spriceType, $aMain['sku'] ?? 0);

                $aGtag          = by::model('GtagModel','goods')->GetListByGid($gid);
                $aGtype['tids'] = [];
                if (!empty($aGtag)) {
                    $tids           = array_column($aGtag, 'tid');
                    $aGtype['tids'] = $tids;
                }

                $aData = array_merge($aMain, $aGtype);
                break;

            default :
                $aData = [];
        }

        if ($is_trade_in) {
            // 以旧换新计算价格
            list($price_status, $aData) = TradeInService::getInstance()->tradeInPrice($aData,$gid,$sid,$format_price);
            if (!$price_status) {
                return [false, $aData];
            }
        }

        // 缓存
        $this->GetOneByGidSidCache[$key] = $aData;

        return $aData;
    }

    /**
     * @param $gid
     * @param bool $check_gt 上下架验证
     * @return array
     * @throws Exception
     * @throws \RedisException
     * 安全下发商品信息
     * 用于商品有效期检验和数据脱敏下发
     */
    public function SafeInfo($gid,$check_gt=true,$arr=[]): array
    {
        $getChannel = $arr['get_channel'] ?? 0;//优惠券来源控制
        $spriceType = $arr['sprice_type'] ?? 0;//自定义设置价格类型
        $user_id    = $arr['user_id']     ?? 0;
        $group_activity_id = $arr['group_activity_id'] ?? 0; //团购


        $aGoods = $this->GetAllOneByGid($gid, false,true,$spriceType);
        if(empty($aGoods)) {
            return [false,"商品不存在"];
        }

        $g_st   = $aGoods['status'] ?? 1;//是否下架 0否1是
        if($check_gt && ($g_st != 0) ) {
            return [false,"商品不存在或已下架"];
        }

        $mainSku = $aGoods['sku'] ?? '';
        $aGoods = $this->GetSpecsPriceInfo($aGoods, $spriceType ?? 0);
        $aGoods['main_sku'] = $mainSku;

        //用户可买限制
        if(strlen($user_id)<12&&!empty($user_id)){
                // 团购限购
            if ($group_activity_id>0){
                $aGoods['limit_num'] = GroupPurchaseService::getInstance()->limitBuy($group_activity_id,$gid,$user_id);
            }else{
                // 普通限购
                $aGoods['limit_num'] = by::Ogoods()->GetLimitBuyGid($user_id,$gid,$aGoods['limit_num']??0,1);

            }
        }

        //不下发字段
        unset($aGoods['ctime']);

        return [true,$aGoods];
    }



    /**
     * @param $version
     * @return array
     * 获取可访问商品版本号
     */
    public function GetAccessVersions($version): array
    {
        $version  = CUtil::version2long($version);
        $version = $version === '' ? 0 : $version;
        return array_unique([0,$version]);
    }

    /**
     * @param $page
     * @param $page_size
     * @param $version  : 当前版本号
     * @param $type     : 商品类型
     * @param $status   : 0上架1下架
     * @param $name     : 商品名称
     * @param $sku      : 商品编码
     * @param $tid      : 标签
     * @return array
     * @throws Exception
     *
     */
    public function GetList(
        $page=1,    $page_size=50, $version='', $type=-1,
        $status=-1, $name='',      $sku='',     $tid=-1,
        $detailData=[], $sort=[]
    ): array
    {

        $r_key   = $this->__getGoodsListKey();
        $sub_key = CUtil::getAllParams(__FUNCTION__,$page,$page_size,$version,$type,$status,$name,$sku,$tid,json_encode($detailData),json_encode($sort));

        $aJson   = by::redis('core')->hGet($r_key,$sub_key);
        $aData   = (array)json_decode($aJson,true);
        if($aJson === false) {
            $tb                  = $this->tbName();
            $t_gtype_0           = Gtype0Model::tbName();
            $t_gtype_99          = Gtype99Model::tbName();
            list($offset)        = CUtil::pagination($page,$page_size);
            list($where, $params)= $this->__getCondition($version, $status, $name, $sku, $tid, $detailData, $type);

             // 排序规则
            $default_sort_rule = '`sort` DESC, `t_gmain`.`id` DESC';

            // 首页排序专用
            if ($tid == -2) {
                // 获取有效标签ID
                $list = by::model('GtagModel', 'goods')->GetTagCnfList();
                $tids = array_filter(array_column($list, 'tid'), function($v) {
                    return $v > 0;
                });

                if (!empty($tids)) {
                    $t_gtag = by::model('GtagModel', 'goods')->tbName();
                    $tag_condition = "`tid` IN (" . implode(',', $tids) . ")";
                    $join_tag = "INNER JOIN {$t_gtag} ON `t_gmain`.`id` = {$t_gtag}.`gid` AND {$t_gtag}.{$tag_condition}";
                    $sort_rule = "FIELD({$t_gtag}.`tid`, " . implode(',', $tids) . "), ".$default_sort_rule;
                }
            } else {
                $sort_rule = empty($sort) ? $default_sort_rule : $sort['sort_field'] . ' ' . $sort['sort_type'] . ', ' . $default_sort_rule;
            }

            $sql = "SELECT `t_gmain`.`id`, COALESCE(`t_gtype_0`.`price`, `t_gtype_99`.`price`) AS price 
                    FROM {$tb} 
                    LEFT JOIN {$t_gtype_0} ON `t_gmain`.`id` = `t_gtype_0`.`gid` 
                    LEFT JOIN {$t_gtype_99} ON `t_gmain`.`id` = `t_gtype_99`.`gid` 
                    " . ($tid == -2 ? $join_tag : "") . "
                    WHERE {$where} 
                    ORDER BY {$sort_rule}
                    LIMIT {$offset},{$page_size}";

            $aData               = by::dbMaster()->createCommand($sql,$params)->queryAll();

            by::redis('core')->hSet($r_key,$sub_key,json_encode($aData));
            CUtil::ResetExpire($r_key,600);
        }

        return empty($aData) ? [] : array_column($aData,"id");
    }


    /**
     * 产品比较少，所以从数据库查找所有的上线商品
     * @param $cache
     * @return array
     * @throws Exception
     */
    public function GetAllList($cache = true): array
    {
        $r_key = $this->__getAllGoodsListKey();
        $aJson = $cache ? by::redis('core')->get($r_key) : false;
        $aData   = (array)json_decode($aJson,true);
        if($aJson === false) {
            $tb                  = $this->tbName();
            $sql = "SELECT `id`,`name`,`sku` FROM {$tb} where `is_del` = 0  AND `status`= 0 ORDER BY `sort` DESC,`id` DESC ";
            $aData               = by::dbMaster()->createCommand($sql)->queryAll();
            by::redis('core')->set($r_key,json_encode($aData));
            CUtil::ResetExpire($r_key,600);
        }
        return $aData ?? [];
    }


    /**
     * @param string $version
     * @param int $type
     * @param int $status
     * @param string $name
     * @param string $sku
     * @param int $tid
     * @return int
     * @throws Exception
     * 商品列表总数
     */
    public function GetListCount($version='', $type=-1, $status=-1, $name='', $sku='', $tid=-1,$detailData=[]) {

        $r_key   = $this->__getGoodsListKey();
        $sub_key = CUtil::getAllParams(__FUNCTION__,$version,$type,$status,$name,$sku,$tid,json_encode($detailData));
        $count   = by::redis('core')->hGet($r_key,$sub_key);
        if($count === false) {
            $tb                  = $this->tbName();
            list($where, $params)= $this->__getCondition($version, $status, $name, $sku, $tid, $detailData, $type);
            $sql                 = "SELECT COUNT(*) FROM {$tb} WHERE {$where}";
            $count               = by::dbMaster()->createCommand($sql,$params)->queryScalar();
            $count               = empty($count) ? 0 : $count;

            by::redis('core')->hSet($r_key,$sub_key,$count);
            CUtil::ResetExpire($r_key,6000);
        }

        return intval($count);
    }


    /**
     * @param $version   :  当前版本号
     * @param $status   :  0上架1下架
     * @param $name     : 商品名称
     * @param $sku      : 商品编码
     * @param $tid      : 商品标签
     * @return array
     * @throws Exception
     * 规范化查询条件
     */
    private function __getCondition($version = '', $status = -1, $name = '', $sku = '', $tid = -1, $detailData = [], $type = -1, $isMain = false, $platformId = 0,$atype = 0): array
    {
        //SQL初始化条件
        $where             = "`t_gmain`.`is_del`=:is_del";
        $params[':is_del'] = 0;

        if(!empty($name)) {
            $where                .= " AND `t_gmain`.`name` LIKE :name";
            $params[":name"]       = "%{$name}%";
        }

        if(!empty($sku)) {
            $where                .= " AND `t_gmain`.`sku` LIKE :sku";
            $params[":sku"]        = "%{$sku}%";
        }

        if($status >= 0) {
            $where                .= " AND `t_gmain`.`status`=:status";
            $params[":status"]     = intval(!!$status);
        }

        if ($tid > 0) {
            $tb                    = by::model('GtagModel', 'goods')->tbName();
            $where                .= " AND `t_gmain`.`id` IN (SELECT `gid` FROM {$tb} WHERE `tid`={$tid})";
        }

        if ($type > -1) {
            $where                .= " AND `t_gmain`.`type` =:type";
            $params[":type"]        = "$type";
        }

        //是否是主机
        if ($isMain) {
            // 获取主标签的 gid 列表
            $mainTag = by::Gtag()->GetMainTag();
            // $tag      = GtagModel::MAIN_TAG;
            $tag      = $mainTag;
            $subQuery = GtagModel::find()->select('gid')->where(['tid' => $tag]);

            // 构建主查询条件
            $where .= " AND `t_gmain`.`id` IN (" . $subQuery->createCommand()->getRawSql() . ")";
        }

        //是否是多规格
        if ($atype) {
            $gids = Gtype0Model::find()->select('gid')->where(['atype' => $atype])->column();
            if (!empty($gids)) {
                $where .= " AND `t_gmain`.`id` NOT IN (" . implode(',', $gids) . ")";
            }
        }

        // 是否是当前平台商品
        if (!empty($platformId)) {
            $gIdsData = byNew::GoodsPlatformModel()->getDataList(['gid'], ['platform_id' => $platformId]);
            $gIds     = array_column($gIdsData, 'gid');
            if (empty($gIds)) {
                $where .= " AND 1=2";
            } else {
                $where .= " AND id IN (" . implode(',', $gIds) . ")";
            }
        }

        if (!empty($detailData) && is_array($detailData)) {
            $isRecommend = $detailData['is_recommend'] ?? -1;
            $isInternalPurchase = intval($detailData['is_internal_purchase'] ?? -1);
            $tb   = by::model('Gtype0Model', 'goods')->tbName();
            $t_goods_platform = GoodsPlatformModel::tbName(); // 商品平台表
            $tb99 = by::model('Gtype99Model', 'goods')->tbName();
            if (intval($isRecommend) >= 0) {
                $where .= " AND `t_gmain`.`id` IN (SELECT `gid` FROM {$tb} WHERE `is_recommend`={$isRecommend} UNION SELECT `gid` FROM {$tb99} WHERE `is_recommend`={$isRecommend})";
            }
            $platformIds = $detailData['platformIds'] ?? [];
            if($platformIds){
                $where .= " AND `t_gmain`.`id` IN (SELECT DISTINCT(`gid`) FROM {$t_goods_platform} WHERE `platform_id` in (".implode(',',$platformIds).") AND `goods_type` = 1)";
            }
            if ($isInternalPurchase >= 0) {
                $where .= " AND `t_gmain`.`id` IN (SELECT `gid` FROM {$tb} WHERE `is_internal_purchase`={$isInternalPurchase} UNION SELECT `gid` FROM {$tb99} WHERE `is_internal_purchase`={$isInternalPurchase})";
            }
            $isPresale= $detailData['is_presale'] ?? -1;
            if ($isPresale>=0) {
                $where .= " AND `t_gtype_0`.`is_presale` = {$detailData['is_presale']}";
            }

        }

        //版本号控制
        $versions  = $this->GetAccessVersions($version);
        $versions  = implode(',',$versions);
        $where    .= " AND `t_gmain`.`version` IN ({$versions})";

        return [$where,$params];
    }

    /**
     * @param $id
     * @param array $update
     * @return array
     * @throws Exception
     * 编辑数据
     */
    public function UpdateData($id, array $update)
    {
        $id     = CUtil::uint($id);
        //允许修改的字段
        if (empty($id) || empty($update)) {
            return [false, '参数缺失'];
        }
        $allowed = ['sort','status'];

        foreach ($update as $field => $val) {
            if ( !in_array($field, $allowed) ) {
                unset($update[$field]);
            }
        }

        if (empty($update)) {
            return [false, '无字段更改'];
        }

        $tb      = self::tbName();

        by::dbMaster()->createCommand()->update($tb,
            $update,
            ['id' => $id]
        )->execute();

        //强制清理缓存
        $this->__delCache($id);
        $this->__delListCache();

        return [true, 'ok'];
    }

    /**
     * @param $gid
     * @param string $code
     * @return array
     * @throws Exception
     * 获取分享二维码
     */
    public function ShareCode($gid, string $code=''): array
    {
        $gInfo          = $this->GetOneByGid($gid);

        if (empty($gInfo) || $gInfo['status'] == 1 || $gInfo['is_del'] == 1) {
            return [false, '该商品已下架'];
        }

        if (!empty($code)) {
            list($ouid,$rand) = by::userGuide()->decrypt($code);

            if (empty($ouid) || empty($rand)) {
                return [false, '分享参数错误'];
            }

            $user       = by::users()->getOneByUid($ouid);
        }

//        $jump_page      = "pages/goodsDetail/goodsDetail";

        $data   = [
            'scene'     => "id={$gid}&o={$code}&union=daogou",
            'nick'      => $user['nick']    ?? '',
            'avatar'    => $user['avatar']  ?? '',
        ];

        /*list($s, $ret)  = WeiXin::factory()->getWxaCodeUnlimit($jump_page,$scene,true);
        if (!$s) {
            return [false, $ret];
        }*/

        return [true, $data];

    }



    /**
     * @param $gid
     * @param string $code
     * @return array
     * @throws Exception
     * 获取分享二维码
     */
    public function InternalShareCode(string $code=''): array
    {
        if (!empty($code)) {
            list($ouid,$rand) = by::userGuide()->decrypt($code);

            if (empty($ouid) || empty($rand)) {
                return [false, '分享参数错误'];
            }

            $user       = by::users()->getOneByUid($ouid);
        }


        $data   = [
            'scene'     => "is_internal_purchase=1&o={$code}&union=neigou",
            'nick'      => $user['nick']    ?? '',
            'avatar'    => $user['avatar']  ?? '',
        ];

        return [true, $data];

    }

    /**
     * @param $user_id
     * @param $source
     * @return array
     * @throws Exception
     * 导购加密串
     */
    public function Scode($user_id, $source): array
    {
        $user_id = CUtil::uint($user_id);
        if ($user_id == 0) {
            return [false, '参数错误'];
        }

        $source = CUtil::uint($source);
        switch ($source) {
            case self::CODE_SOURCE['GUIDE'] :
                $user = by::guide()->getGuideInfo($user_id);
                if (empty($user)) {
                    return [false, '暂未开放(1)'];
                }

                if ($user['status'] != 0) {
                    return [false, '暂未开放(2)'];
                }

                break;
            case self::CODE_SOURCE['REFERRER'] :
                $user = by::users()->getUserMainInfo($user_id);
                if (empty($user)){
                    return [false,'推荐人不存在'];
                }

                break;
            default :
                return [false, '来源不合法'];
        }

        $rand   = CUtil::createVerifyCode(3,1);
        $params = CUtil::getAllParams($user_id,$rand);
        $encode = CUtil::encrypt($params,self::SHARE_KEY);

        return [true, ['code' => $encode]];

    }

    /**
     * @param $ids
     * @param $act
     * @return array
     * @throws Exception
     * @throws RedisException
     * 批量操作商品
     */
    public function batchPut($ids, $act)
    {
        if (empty($ids)) {
            return [false, '请选择商品操作'];
        }

        if ( !in_array($act, self::ACT) ) {
            return [false, '操作码有误'];
        }

        $ida    = explode(',', $ids);
        $tb     = self::tbName();
        $where  = ['id' => $ida];

        switch ($act) {
            case self::ACT['UP'] :
                $data = ['status' => 0];
                break;
            case self::ACT['DOWN'] :
                $data = ['status' => 1];
                break;
            default :
                return [false, '不支持的操作'];
        }

        $num = by::dbMaster()->createCommand()->update($tb,
            $data,
            $where
        )->execute();

        //推送OMS
        $lockOldErp = CUtil::omsLock(0,time());

        $mainIds = [];

        foreach($ida as $gid) {
            if($lockOldErp){
                ErpNew::factory()->pushGoods($gid);
            }
            $this->__delCache($gid);
            $aLog   = $this->GetOneByGid($gid);
            $main_skus = by::mainPartModel()->getMainByPartSku($aLog['sku'] ?? '');
            $main_skus = array_column($main_skus,'main_sku');
            by::mainPartModel()->__delGetInfoByMainSku($main_skus);
            $gCate = by::gCateModel()->getCateInfoBySku($aLog['sku'] ?? '') ?? [];
            //配件陈列更改gcate上下架状态
            if ($gCate){
                $gWhere = ['sku'=>$aLog['sku']];
                $g_cate_tb = by::gCateModel()::tbName();
                by::dbMaster()->createCommand()->update($g_cate_tb,
                    $data,
                    $gWhere
                )->execute();
                by::gCateModel()->__delCateInfoBySkuCache(explode(',',$aLog['sku'] ?? ''));
                by::gCateModel()->__delCateInfoByCidCache($gCate['c_id'] ?? 0);
            }

            //产品注册更改配件上下架状态
            $partInfo = by::Gtag()->getGidInfo(by::partsSalesModel()::PART_TYPE);
            $partGid = array_unique(array_column($partInfo,'gid'));
            if (in_array($gid,$partGid)){
                $mainIds = by::partsSalesModel()->getMainIdBySku($aLog['sku'],by::partsSalesModel()::PART_TYPE);
                if ($mainIds){
                    $partWhere = ['main_id'=>$mainIds,'part_sku'=>$aLog['sku']];
                    $part_sales_tb = by::partsSalesModel()::tbName();
                    by::dbMaster()->createCommand()->update($part_sales_tb,
                        $data,
                        $partWhere
                    )->execute();
                }
            }
            switch ($aLog['type']) {
                case self::TYPE['COMMON'] :
                    by::Gtype0()->UpdateData($gid, ['t_status' => 0]);
                    break;
                case self::TYPE['Y_LINK'] :
                    by::Gtype99()->UpdateData($gid, ['t_status' => 0]);
                    break;
            }
        }
        $this->__delListCache();
        by::partsSalesModel()->__delHostAndPartCache();
        by::partsSalesModel()->__delGetOneCache($mainIds);
        by::mainSalesModel()->__delGetMainInfoByIdCache($mainIds);
        by::cateModel()->__delGetListCache();

        return [true, $num];
    }

    /**
     * @param $id
     * @return array
     * @throws Exception
     * 删除
     */
    public function Del($id)
    {
        $id     = CUtil::uint($id);
        if (empty($id)) {
            return [false, '参数错误'];
        }

//        $aLog   = $this->GetOneByGid($id);
        $aLog   = $this->GetAllOneByGid($id);
        if (empty($aLog)) {
            return [false, '参数错误(1)'];
        }

        $db     = by::dbMaster();
        $tb     = self::tbName();

        $mSpecs = by::Gspecs();

        $trans  = $db->beginTransaction();

        try {
            by::dbMaster()->createCommand()->update($tb,
                ['is_del' => 1],
                ['id' => $id]
            )->execute();

            if ($aLog['atype'] != by::Gtype0()::ATYPE['SPEC']) {
                $specs  = by::Gspecs()->GetListByGid($id);
                if ($specs) {
                    foreach ($specs as $spec) {
                        $mSpecs->UpdateLog($spec['gid'], $spec['sku'], ['is_del' => 1]);
                    }
                }
            }

            $trans->commit();

            $this->__delCache($id);
            $this->__delListCache();
            $this->__delSkuCache(json_encode([$aLog['sku']]));

            return [true, 'ok'];

        } catch (\Exception $e) {
            $trans->rollBack();

            CUtil::debug($e->getMessage(), 'err.gdel');

            return [false, '操作失败'];
        }

    }

    /**
     * @param $ck_code
     * @param string $skus
     * @return array
     * @throws \yii\db\Exception
     * @throws Exception
     * 校验是否是正确的sku
     */
    public function IsSku($ck_code, string $skus)
    {
        if (empty($ck_code) || empty($skus)) {
            return [false, '参数错误'];
        }

        $skus   = explode(',', $skus);

        $r_sku  = [];

        $lockOldErp = CUtil::omsLock(0,time());
        foreach($skus as $sku) {
            if($lockOldErp){
                break;
            }
            //todo erp查库存-优化点：可一次性查
            list($s, $stock_num) = Erp::factory()->getStockNew($sku,$ck_code,true);
            if (!$s) {
                $r_sku[] = $sku;
            }
        }

        $data   = [
            'code'  => empty($r_sku) ? '1' : '0',
            'sku'   => implode(',', $r_sku)
        ];

        return [true, $data];
    }

    /**
     * @throws \yii\db\Exception
     * 同步库存
     * 后续可以改，先将店铺的所有库存拉下来后匹配-看线上数据量多少
     */
    public function SyncStock()
    {
        $mGstock    = by::Gstock();
        $db         = by::dbMaster();
        $tb         = self::tbName();
        $id         = 0;
        $sql        = "SELECT `id` FROM {$tb} WHERE `id` > :id AND `status` = 0 AND is_del = 0 ORDER BY `id` LIMIT 100";

        while (true) {
            $list   = $db->createCommand($sql, [':id' => $id])->queryAll();
            if (empty($list)) {
                break;
            }

            $end    = end($list);
            $id     = $end['id'];

            foreach($list as $v) {
                $mGstock->SyncStock($v['id']);
            }

        }

    }

    /**
     * @param string $version
     * @param int $type
     * @param int $status
     * @param string $name
     * @param string $sku
     * @param int $tid
     * @throws Exception
     * 导出
     */
    public function export($version='',$type=-1,$status=-1,$name='',$sku='',$tid=-1)
    {
        $head   = [
            '商品ID','编号','名称','标签','初始价','优惠价','规格类型','总库存','剩余库存','销量','限购数','优惠券','上下架','排序'
        ];

        $f_name = '商品列表' . date('Ymd') . mt_rand(1000, 9999);

        list($where, $params) = $this->__getCondition($version, $status, $name, $sku, $tid, $type);

        //导出
        CUtil::export_csv_new($head, function () use($where, $params) {

            $db         = by::dbMaster();
            $tb         = self::tbName();
            $mGmain     = by::Gmain();

            $goodsMainService = GoodsMainService::getInstance();
            $source = by::GoodsStockModel()::SOURCE['MAIN'];
            $tagMap = by::Gtag()->GetTagNameMap();
            $tag_names  = $tagMap;

            $id     = 0;
            $sql    = "SELECT `id` FROM {$tb} WHERE `id` > :id AND {$where} ORDER BY `id` LIMIT 200";

            while (true) {
                $params['id']   = $id;
                $list = $db->createCommand($sql, $params)->queryAll();

                if (empty($list)) {
                    break;
                }

                $end        = end($list);
                $id         = $end['id'];

                $data       = [];
                foreach ($list as $val) {
                    $aGoods                 = $mGmain->GetAllOneByGid($val['id']);

                    list($stock, $sales)    = $goodsMainService->GetSumByGid($val['id'],$source);

                    $tag_name   =  array_map(function ($v) use($tag_names) {
                        return $tag_names[$v] ?? $v;
                    }, $aGoods['tids']);

                    $data[] = [
                        'gid'       => $val['id'],
                        'sku'       => $aGoods['sku'],
                        'name'      => $aGoods['name'],
                        'tids'      => implode('、',$tag_name),
                        'mprice'    => $aGoods['mprice'],
                        'price'     => $aGoods['price'],
                        'atype'     => $aGoods['atype'] == 0 ?'统一规格' :'自定义',
                        'allStock'  => bcadd($stock,$sales),
                        'stock'     => $stock,
                        'sales'     => $sales,
                        'limit_num' => $aGoods['limit_num'],
                        'is_coupons'=> $aGoods['is_coupons'] == 1 ? '可用' : '不可用',
                        'status'    => $aGoods['status'] == 0 ? '上架' : '下架',
                        'sort'      => $aGoods['sort'],
                    ];
                }

                yield $data;
            }

        }, $f_name);
    }

    public function exportData($version='',$type=-1,$status=-1,$name='',$sku='',$tid=-1)
    {
        $head   = [
            '商品ID','编号','名称','标签','初始价','优惠价','规格类型','总库存','剩余库存','销量','限购数','优惠券','上下架','排序'
        ];


        list($where, $params) = $this->__getCondition($version, $status, $name, $sku, $tid, $type);

        //导出
        $db         = by::dbMaster();
        $tb         = self::tbName();
        $mStock     = by::Gstock();
        $mGmain     = by::Gmain();
        $goodsMainService = GoodsMainService::getInstance();
        $source = by::GoodsStockModel()::SOURCE['MAIN'];
        $tagMap = by::Gtag()->GetTagNameMap();
        $tag_names  = $tagMap;

        $id     = 0;
        $sql    = "SELECT `id` FROM {$tb} WHERE `id` > :id AND {$where} ORDER BY `id` LIMIT 200";

        $data[] = $head;
        while (true) {
            $params['id']   = $id;
            $list = $db->createCommand($sql, $params)->queryAll();

            if (empty($list)) {
                break;
            }

            $end        = end($list);
            $id         = $end['id'];

            foreach ($list as $val) {
                $aGoods                 = $mGmain->GetAllOneByGid($val['id']);

                list($stock, $sales)    = $goodsMainService->GetSumByGid($val['id'],$source);

                $tag_name   =  array_map(function ($v) use($tag_names) {
                    return $tag_names[$v] ?? $v;
                }, $aGoods['tids']);

                $data[] = [
                    'gid'       => $val['id'],
                    'sku'       => $aGoods['sku'],
                    'name'      => $aGoods['name'],
                    'tids'      => implode('、',$tag_name),
                    'mprice'    => $aGoods['mprice'],
                    'price'     => $aGoods['price'],
                    'atype'     => $aGoods['atype'] == 0 ?'统一规格' :'自定义',
                    'allStock'  => bcadd($stock,$sales),
                    'stock'     => $stock,
                    'sales'     => $sales,
                    'limit_num' => $aGoods['limit_num'],
                    'is_coupons'=> $aGoods['is_coupons'] == 1 ? '可用' : '不可用',
                    'status'    => $aGoods['status'] == 0 ? '上架' : '下架',
                    'sort'      => $aGoods['sort'],
                ];
            }
        }
        return $data;
    }


    /**
     * @throws Exception
     */
    public function getRecommendList($ip, $userId, $post, $detailData, $version): array
    {
        $data = [];
         //1.防止高并发(暂时不限制)
        $mark = empty($userId) ? $ip : $userId;
        $page                      = $post['page'] ?? 1;
        $tid                       = $post['tid'] ?? -1;
        $is_recommend              = $post['is_recommend'] ?? 1;
        $page_size                 = $post['page_size'] ?? 200;
        $status                    = $post['status'] ?? 0;
        $type                      = $post['type'] ?? 0;

        //2.数据查询
        //先查询数量，没有数量返回为空
        $count           = $this->GetListCount($version, $type, $status, '', '', $tid,$detailData);
        if($count){
            //2.查询数据
            $gids = $this->GetList($page, $page_size, $version, $type, $status, '', '', $tid, $detailData);
            $data['list'] = $this->GetListByGids($gids,$post);
        }
        $data['count'] = $count;

        return [true, $data];
    }

    /**
     * 获取商品列表
     * @param array $gids
     * @return array
     * @throws Exception
     * @throws RedisException
     */
    public function GetListByGids(array $gids, $post= []): array
    {
        $list = [];
        foreach ($gids as $gid) {
            $aGoods = $this->GetAllOneByGid($gid, true, true, $post['sprice_type'] ?? 0);
            if (!$aGoods) {
                continue;
            }
            //t_gmain中sku
            $sku = $aGoods['sku'] ?? '';
            $aGoods = $this->GetSpecsPriceInfo($aGoods, $post['sprice_type'] ?? 0);
            $aGoods = [
                'gid'                  => $aGoods['gid'],
                'sku'                  => $sku,
                'name'                 => $aGoods['name'],
                'cover_image'          => $aGoods['cover_image'],
                'market_image'         => empty($aGoods['market_image'] ?? '') ? $aGoods['cover_image'] : $aGoods['market_image'] ?? '',
                'mprice'               => $aGoods['mprice'],
                'price'                => $aGoods['price'],
                'uprice'               => $aGoods['price'],
                'tids'                 => $aGoods['tids'],
                'is_internal'          => $aGoods['is_internal'] ?? 0,
                'is_internal_purchase' => $aGoods['is_internal_purchase'] ?? 0,
                'is_ini'               => $aGoods['is_ini'] ?? 0,
                'gini_id'              => $aGoods['gini_id'] ?? 0,
                'is_presale'           => $aGoods['is_presale'] ?? 0,
                'second_cate_id'       => $aGoods['cate'][1] ?? 0,
                'status'               => $aGoods['status'], // 商品状态
            ];
            $list[] = $aGoods;
        }
        return $list;
    }

    public function getShortCodeBySku($sku)
    {
        $tb = self::tbName();
        $sql = "SELECT `short_code` FROM {$tb} WHERE `sku` =:sku";
        $command = by::dbMaster()->createCommand($sql,[':sku'=>$sku])->queryOne();
        return $command['short_code'] ?? '';
    }

    /**
     * 根据 cate_ids 获取普通商品信息
     * @param array $cateIds
     * @return array
     */
    public function getDataByCateIds(array $cateIds): array
    {
        $gCateItems = [];
        foreach ($cateIds as $cateId) {
            $items = by::gCateModel()->getCateInfoByCid($cateId);
            if (empty($items)) {
                continue;
            }
            foreach ($items as $item) {
                if (($item['status'] == self::STATUS['OFF_SALE']) ||
                    ($item['is_del'] == self::IS_DEL['yes'])) { // 下架、删除
                    continue;
                }
                $gCateItems[$item['sku']] = [
                    'sku'  => $item['sku'],
                    'c_id' => $item['c_id'],
                ];
            }
        }

        // 获取商品信息
        $data = [];
        $items = $this->GetAllList(true);
        foreach ($items as $item) {
            if (!isset($gCateItems[$item['sku']])) {
                continue;
            }
            $gCateItem = $gCateItems[$item['sku']];
            $data[] = [
                'gid'  => $item['id'],
                'sku'  => $item['sku'],
                'name' => $item['name'],
                'c_id' => $gCateItem['c_id'],
            ];
        }
        return $data;
    }


    /**
     * @throws \yii\db\Exception
     * @throws \RedisException
     */
    public function GetSkusByGid($gid): array
    {
        $aMain = $this->GetOneByGid($gid);
        if(empty($aMain)){
            return [];
        }
        //商品信息
        $type = $aMain['type'] ?? 0;
        switch ($type) {
            case self::TYPE['COMMON'] :
                    $aGtype = by::Gtype0()->GetOneBaseByGid($gid);
                break;
            case self::TYPE['Y_LINK'] :
                    $aGtype = by::Gtype99()->GetOneBaseByGid($gid);
                break;
            default :
                return [];
        }

        switch ($aGtype['atype']) {
            case by::Gtype0()::ATYPE['SPEC']:
                //库存
                $sku                = $aMain['sku'] ?? '';
                $skus = empty($sku) ? []: [$sku];
                break;
            case by::Gtype0()::ATYPE['SPECS']:
                $specs = by::Gspecs()->GetListByGid($gid);
                $skus = empty($specs) ? [] : array_column($specs,'sku');
                break;
            default:
                return [];
        }
        return $skus;
    }


    public function GetGidAndSidBySku($sku): array
    {
        $arr = [
            'gid'=>0,
            'sid'=>0
        ];
        $mainSku = by::Gmain()->GetOneBySku($sku);
        $arr['gid'] = $mainSku['id'] ?? 0;
        if (empty($mainSku)) {
            $mainSku = by::Gspecs()->GetOneBySku($sku);
            $arr['sid'] = $mainSku['id'] ?? 0;
            $arr['gid'] = $mainSku['gid'] ?? 0;
        }
        return $arr;
    }

    // 根据ids获取商品信息
    public function getDataByIds(array $ids, array $columns = ['*'])
    {
        // 判空
        if (empty($ids)) {
            return [];
        }

        // 查询
        return self::find()->select($columns)->where(['id' => $ids])->asArray()->all();
    }

    // 根据sku获取商品信息
    public function getProductInfoBySku(array $skus, array $columns = ['*'])
    {
        // 判空
        if (empty($skus)) {
            return [];
        }

        $tbMain = self::tbName(); // 商品主表
        $tbSpecs = GspecsModel::tbName(); // 商品规格表
        $skus = implode('","', $skus);

        // 查询字段
        $columns = implode(',', $columns);

        try {
            $sql = "SELECT {$columns}
            FROM {$tbMain} AS main
            LEFT JOIN {$tbSpecs} AS specs ON main.id = specs.gid
            WHERE
                main.sku IN (\"{$skus}\")
                OR specs.sku IN (\"{$skus}\")
            ORDER BY main.id DESC, specs.id DESC";

            $res = by::dbMaster()->createCommand($sql)->queryAll();
            return $res;
        } catch (\Exception $e) {
            return [];
        }
    }

    /**
     * 根据名称获取商品信息
     * @param string $name
     * @param array $columns
     * @return array|DataReader
     */
    public function getProductInfoByName(string $name, array $columns = ['*'])
    {
        // 判空
        if (empty($name)) {
            return [];
        }

        $tbMain = self::tbName(); // 商品主表
        $columns = implode(',', $columns);  // 查询字段

        try {
            $sql = "SELECT {$columns}
            FROM {$tbMain}
            WHERE
                name LIKE '%{$name}%'";
            $res = by::dbMaster()->createCommand($sql)->queryAll();
            return $res;
        } catch (\Exception $e) {
            return [];
        }
    }

    /**
     * 获取有效的sku
     * @param int $gid
     * @param int $goods_type 商品类型 1普通商品 2积分商品
     * @return array
     */
    public function getEffectiveSkuByGid(int $gid, int $goods_type): array
    {
        try {
            if ($goods_type == 1) { // 普通商品
                $main = $this->GetAllOneByGid($gid, true, false, 0, false);
                if (empty($main)) {
                    return [];
                }
                if ($main['atype'] == by::Gtype0()::ATYPE['SPEC']) { // 单一规格
                    return [$main['sku']];

                } else { // 多规格
                    $specs = by::Gspecs()->GetListByGid($gid);
                    return array_column($specs, 'sku');
                }
            } else { // 积分商品
                $skus = by::GoodsPointsPriceModel()->GetSkusByGids($gid);
                return array_unique($skus);
            }
        } catch (\Exception $e) {
            return [];
        }
    }


    /**
     * @param $page
     * @param $pageSize
     * @param $version
     * @param $status
     * @param $type
     * @param bool $isMain
     * @param $platformId
     * @param $aType
     * @return array
     * @throws Exception
     * @throws RedisException 获取默认商品列表（PC商城加入购物车推荐+搜索默认推荐）
     */
    public function GetDefaultGoodsList($page, $pageSize, $version, $status, $type, bool $isMain = true, $platformId = 0, $aType = 0): array
    {
        $r_key   = $this->__getGoodsListKey();
        $sub_key = CUtil::getAllParams(__FUNCTION__, $page, $pageSize, $version, $type, $status, $isMain, $platformId, $aType);

        // 尝试从缓存中获取数据
        $aJson = by::redis('core')->hGet($r_key, $sub_key);
        if ($aJson !== false) {
            $aData = (array) json_decode($aJson, true);
            return empty($aData) ? [] : array_column($aData, "id");
        }

        // 从数据库获取数据
        $tb = $this->tbName();
        list($offset) = CUtil::pagination($page, $pageSize);
        list($where, $params) = $this->__getCondition($version, $status, '', '', '', [], $type, $isMain, $platformId, $aType);
        $sql   = "SELECT `id` FROM {$tb} WHERE {$where} ORDER BY `id` DESC LIMIT {$offset}, {$pageSize}";
        $aData = by::dbMaster()->createCommand($sql, $params)->queryAll();

        // 将数据存入缓存
        by::redis('core')->hSet($r_key, $sub_key, json_encode($aData));
        CUtil::ResetExpire($r_key, 600);

        return empty($aData) ? [] : array_column($aData, "id");
    }

    /**
     * 商品标签
     * @param int $gid
     * @return array[]
     * @throws Exception
     */
    public function getProductTags(int $gid): array
    {
        // 结果数据
        $data = [
            'tids'      => [],
            'tids_name' => []
        ];
        // 获取商品标签
        $list = by::Gtag()->GetListByGid($gid);

        if ($list) {
            $tids = array_column($list, 'tid');
            $data['tids'] = $tids;
            $tagMap = by::Gtag()->GetTagNameMap();
            foreach ($tids as $tid) {
                $name = $tagMap[$tid] ?? '';
                $data['tids_name'][] = [
                    'tid'  => $tid,
                    'name' => $name
                ];
            }
        }
        return $data;
    }

}
