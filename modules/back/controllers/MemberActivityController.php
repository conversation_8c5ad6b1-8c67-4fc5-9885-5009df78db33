<?php

namespace app\modules\back\controllers;

use app\models\BusinessException;
use app\modules\back\forms\memberActiviry\MemberActifityForm;
use app\modules\back\forms\memberActiviry\MemberActifityModuleRelationForm;
use app\modules\back\services\MemberActivityService;
use app\modules\common\ControllerTrait;
use app\modules\rbac\controllers\CommController;
use OpenApi\Annotations as OA;

/**
 * 会员活动配置
 * 接口文档：https://doc.apipost.net/docs/detail/43be00f208e0000?target_id=3bdf4588b24177&locale=zh-cn
 * 接口文档密码：388994
 */
class MemberActivityController extends CommController
{
    use ControllerTrait;
    
    /** @var MemberActivityService */
    private $service;
    
    public function __construct($id, $module, $config = [])
    {
        $this->service = MemberActivityService::getInstance();
        parent::__construct($id, $module, $config);
    }

    /**
     * @OA\Post(
     *     path="/back/member-activity/list",
     *     summary="获取主题活动列表",
     *     description="获取主题活动列表",
     *     tags={"后台活动"},
     *     security={
     *         {"sessid": {}},
     *     },
     *     @OA\RequestBody(
     *     @OA\MediaType(  mediaType="application/x-www-form-urlencoded",
     *       @OA\JsonContent(
     *          allOf={
     *          @OA\Schema(ref="#/components/schemas/BaseRequest"),
     *       }
     *      )
     *     )
     * ),
     *     @OA\Response(
     *         response="200",
     *         description="状态码、提示消息",
     *        @OA\JsonContent(
     *             type="object",
     *              @OA\Property(property="iRet",type="integer",description="状态码"),
     *              @OA\Property(property="sMsg",type="string",description="提示消息"),
     *              @OA\Property(property="data",type="array",description="活动列表数据",
     *                  @OA\Items(
     *                      type="object",
     *                      @OA\Property(property="id", type="string", example="1", description="活动ID"),
     *                      @OA\Property(property="title", type="string", example="测试活动111", description="活动标题"),
     *                      @OA\Property(property="description", type="string", example="这是一个副标题", description="活动副标题"),
     *                      @OA\Property(property="rule", type="string", description="活动规则"),
     *                      @OA\Property(property="cycle_type", type="string", example="once", description="活动周期类型"),
     *                      @OA\Property(property="start_time", type="string", example="1747554699", description="活动开始时间时间戳"),
     *                      @OA\Property(property="end_time", type="string", example="1747554699", description="活动结束时间时间戳"),
     *                      @OA\Property(property="status", type="integer", example=0, description="活动状态：0-未开始，1-进行中，2-已结束"),
     *                      @OA\Property(property="status_text", type="string", example="未开始", description="活动状态文本")
     *                  )
     *              )
     *         )
     *     )
     * )
     */
    public function actionList()
    {
        $post = \Yii::$app->request->post();
        $this->success($this->service->getList($post));
    }

    /**
     * @OA\Post(
     *     path="/back/member-activity/detail",
     *     summary="获取主题活动详情",
     *     description="获取主题活动详情",
     *     tags={"后台活动"},
     *     security={
     *         {"sessid": {}},
     *     },
     *     @OA\RequestBody(
     *         @OA\MediaType(mediaType="application/x-www-form-urlencoded",
     *             @OA\Schema(
     *                 @OA\Property(property="id", type="integer", description="活动ID"),
     *                 required={"id"}
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response="200",
     *         description="状态码、提示消息",
     *         @OA\JsonContent(
     *             type="object",
     *             @OA\Property(property="iRet", type="integer", description="状态码"),
     *             @OA\Property(property="sMsg", type="string", description="提示消息"),
     *             @OA\Property(property="data", type="object", description="活动详情数据",
     *                 @OA\Property(property="id", type="string", example="142", description="活动ID"),
     *                 @OA\Property(property="title", type="string", example="测试活动123123", description="活动标题"),
     *                 @OA\Property(property="description", type="string", example="这是一个副标题666612", description="活动副标题"),
     *                 @OA\Property(property="rule", type="string", description="活动规则"),
     *                 @OA\Property(property="cycle_type", type="string", example="once", description="活动周期类型"),
     *                 @OA\Property(property="start_time", type="string", example="1744356288", description="活动开始时间时间戳"),
     *                 @OA\Property(property="end_time", type="string", example="1746860916", description="活动结束时间时间戳"),
     *                 @OA\Property(property="create_time", type="string", example="1745209859", description="创建时间时间戳"),
     *                 @OA\Property(property="update_time", type="string", example="1745209859", description="更新时间时间戳"),
     *                 @OA\Property(property="modules", type="array", description="活动模块列表",
     *                     @OA\Items(
     *                         oneOf={
     *                             @OA\Schema(ref="#/components/schemas/ModuleBigTitle"),
     *                             @OA\Schema(ref="#/components/schemas/ModuleCheckin"),
     *                             @OA\Schema(ref="#/components/schemas/ModuleReturnPoints"),
     *                             @OA\Schema(ref="#/components/schemas/ModuleAdPosition"),
     *                             @OA\Schema(ref="#/components/schemas/ModuleDraw"),
     *                             @OA\Schema(ref="#/components/schemas/ModuleNewPerson"),
     *                             @OA\Schema(ref="#/components/schemas/ModuleRecommendGoods"),
     *                             @OA\Schema(ref="#/components/schemas/ModuleCoupon"),
     *                             @OA\Schema(ref="#/components/schemas/ModulePartGoods"),
     *                             @OA\Schema(ref="#/components/schemas/ModulePointsExchange")
     *                         }
     *                     )
     *                 )
     *             )
     *         )
     *     )
     * )
     */
    public function actionDetail()
    {
        try {
            $post = \Yii::$app->request->post();
            $id = $post['id'] ?? 0;
            if (empty($id)) {
                return $this->error('缺少活动ID参数');
            }
            $this->success($this->service->getDetail($id));
        } catch (\Throwable $e) {
            $this->error($e->getMessage());
        }
    }
    
    /**
     * @OA\Post(
     *     path="/back/member-activity/create",
     *     summary="添加主题活动",
     *     description="添加主题活动",
     *     tags={"后台活动"},
     *     security={
     *         {"sessid": {}},
     *     },
     *     @OA\RequestBody(
     *         @OA\MediaType(mediaType="application/x-www-form-urlencoded",
     *             @OA\Schema(
     *                 allOf={
     *                     @OA\Schema(ref="#/components/schemas/BaseRequest"),
     *                 },
     *                 @OA\Property(property="title", type="string", description="活动标题"),
     *                 @OA\Property(property="description", type="string", description="活动副标题"),
     *                 @OA\Property(property="start_time", type="integer", description="活动开始时间"),
     *                 @OA\Property(property="end_time", type="integer", description="活动结束时间"),
     *                 @OA\Property(property="cycle_type", type="string", description="活动周期类型"),
     *                 @OA\Property(property="rule", type="string", description="活动规则"),
     *                 @OA\Property(property="modules", type="string", description="活动模块配置"),
     *                 required={"title", "description", "start_time", "end_time", "cycle_type", "rule", "modules"}
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response="200",
     *         description="状态码、提示消息",
     *         @OA\JsonContent(
     *             type="object",
     *             @OA\Property(property="iRet", type="integer", description="状态码"),
     *             @OA\Property(property="sMsg", type="string", description="提示消息"),
     *             @OA\Property(property="data", type="object", 
     *                 @OA\Property(property="id", type="integer", description="新创建的活动ID")
     *             )
     *         )
     *     )
     * )
     */
    public function actionCreate()
    {
        try {
            $post = \Yii::$app->request->post();
            $form = new MemberActifityForm();
            $form->setScenario('create');
            if (! ($form->load($post, '') && $form->validate())) {
                $errors = $form->firstErrors;
                $this->error(array_shift($errors));
            }

            list($status, $id, $msg) = $this->service->create($form->toArray());
            if (! $status) {
                throw new BusinessException($msg);
            }
            $this->success(['id' => (int) $id]);
        } catch (\Throwable $e) {
            $this->error($e->getMessage());
        }
    }
    
    /**
     * @OA\Post(
     *     path="/back/member-activity/update",
     *     summary="更新主题活动",
     *     description="更新主题活动",
     *     tags={"后台活动"},
     *     security={
     *         {"sessid": {}},
     *     },
     *     @OA\RequestBody(
     *         @OA\MediaType(mediaType="application/x-www-form-urlencoded",
     *             @OA\Schema(
     *                 allOf={
     *                     @OA\Schema(ref="#/components/schemas/BaseRequest"),
     *                 },
     *                 @OA\Property(property="id", type="integer", description="活动ID"),
     *                 @OA\Property(property="title", type="string", description="活动标题"),
     *                 @OA\Property(property="description", type="string", description="活动副标题"),
     *                 @OA\Property(property="start_time", type="integer", description="活动开始时间"),
     *                 @OA\Property(property="end_time", type="integer", description="活动结束时间"),
     *                 @OA\Property(property="cycle_type", type="string", description="活动周期类型 once=单次"),
     *                 @OA\Property(property="rule", type="string", description="活动规则"),
     *                 required={"id", "title", "description", "start_time", "end_time", "cycle_type", "rule"}
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response="200",
     *         description="状态码、提示消息",
     *         @OA\JsonContent(
     *             type="object",
     *             @OA\Property(property="iRet", type="integer", description="状态码"),
     *             @OA\Property(property="sMsg", type="string", description="提示消息")
     *         )
     *     )
     * )
     */
    public function actionUpdate()
    {
        try {
            $post = \Yii::$app->request->post();
            $form = new MemberActifityForm();
            $form->setScenario('update');
            if (! ($form->load($post, '') && $form->validate())) {
                $errors = $form->firstErrors;
                $this->error(array_shift($errors));
            }
            
            list($status, $msg) = $this->service->update($form->toArray());
            if (! $status) {
                throw new BusinessException($msg);
            }
            $this->success();
        } catch (\Throwable $e) {
            $this->error($e->getMessage());
        }
    }

    /**
     * @OA\Post(
     *     path="/back/member-activity/update-v2",
     *     summary="更新主题活动v2",
     *     description="更新主题活动v2",
     *     tags={"后台活动"},
     *     security={
     *         {"sessid": {}},
     *     },
     *     @OA\RequestBody(
     *         @OA\MediaType(mediaType="application/x-www-form-urlencoded",
     *             @OA\Schema(
     *                 allOf={
     *                     @OA\Schema(ref="#/components/schemas/BaseRequest"),
     *                 },
     *                 @OA\Property(property="id", type="integer", description="活动ID"),
     *                 @OA\Property(property="title", type="string", description="活动标题"),
     *                 @OA\Property(property="description", type="string", description="活动副标题"),
     *                 @OA\Property(property="start_time", type="integer", description="活动开始时间"),
     *                 @OA\Property(property="end_time", type="integer", description="活动结束时间"),
     *                 @OA\Property(property="cycle_type", type="string", description="活动周期类型 once=单次"),
     *                 @OA\Property(property="rule", type="string", description="活动规则"),
     *                 required={"id", "title", "description", "start_time", "end_time", "cycle_type", "rule"}
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response="200",
     *         description="状态码、提示消息",
     *         @OA\JsonContent(
     *             type="object",
     *             @OA\Property(property="iRet", type="integer", description="状态码"),
     *             @OA\Property(property="sMsg", type="string", description="提示消息")
     *         )
     *     )
     * )
     */
    public function actionUpdateV2()
    {
        try {
            $post = \Yii::$app->request->post();
            $form = new MemberActifityForm();
            $form->setScenario('updatev2');
            if (! ($form->load($post, '') && $form->validate())) {
                $errors = $form->firstErrors;
                $this->error(array_shift($errors));
            }

            list($status, $msg) = $this->service->updateV2($form->toArray());
            if (! $status) {
                throw new BusinessException($msg);
            }
            $this->success();
        } catch (\Throwable $e) {
            $this->error($e->getMessage());
        }
    }

    /**
     * @OA\Post(
     *     path="/back/member-activity/delete",
     *     summary="删除主题活动",
     *     description="删除主题活动",
     *     tags={"后台活动"},
     *     security={
     *         {"sessid": {}},
     *     },
     *     @OA\RequestBody(
     *         @OA\MediaType(mediaType="application/x-www-form-urlencoded",
     *             @OA\Schema(
     *                 @OA\Property(property="ids", type="string", description="活动ID，多个ID用逗号分隔"),
     *                 required={"ids"}
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response="200",
     *         description="状态码、提示消息",
     *         @OA\JsonContent(
     *             type="object",
     *             @OA\Property(property="iRet", type="integer", description="状态码"),
     *             @OA\Property(property="sMsg", type="string", description="提示消息")
     *         )
     *     )
     * )
     */
    public function actionDelete()
    {
        try {
            $post = \Yii::$app->request->post();
            $ids = $post['ids'] ?? '';
            if (empty($ids)) {
                return $this->error('缺少ids字段');
            }
            $ids = explode(',', (string) $ids);
            $ids = array_unique($ids);

            list($status, $msg) = $this->service->delete($ids);
            if (! $status) {
                throw new BusinessException($msg);
            }
            $this->success();
        } catch (\Throwable $e) {
            $this->error($e->getMessage());
        }
    }
    
    /**
     * @OA\Post(
     *     path="/back/member-activity/module-res-list",
     *     summary="获取模块资源列表",
     *     description="获取模块资源列表",
     *     tags={"后台活动"},
     *     security={
     *         {"sessid": {}},
     *     },
     *     @OA\RequestBody(
     *         @OA\MediaType(mediaType="application/x-www-form-urlencoded",
     *             @OA\Schema(
     *                 allOf={
     *                     @OA\Schema(ref="#/components/schemas/BaseRequest"),
     *                 }
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response="200",
     *         description="状态码、提示消息",
     *         @OA\JsonContent(
     *             type="object",
     *             @OA\Property(property="iRet", type="integer", description="状态码"),
     *             @OA\Property(property="sMsg", type="string", description="提示消息"),
     *             @OA\Property(property="data", type="array", description="模块资源列表", 
     *                 @OA\Items(
     *                     type="object",
     *                     @OA\Property(property="id", type="string", example="1", description="模块资源ID"),
     *                     @OA\Property(property="module_name", type="string", example="大标题模块", description="模块名称"),
     *                     @OA\Property(property="module_code", type="string", example="BIG_TITLE", description="模块编码")
     *                 )
     *             )
     *         )
     *     )
     * )
     */
    public function actionModuleResList()
    {
        try {
            $params = \Yii::$app->request->post();
            $this->success($this->service->getModuleResList($params));
        } catch (\Throwable $e) {
            $this->error($e->getMessage());
        }
    }
    
    /**
     * @OA\Post(
     *     path="/back/member-activity/task-list",
     *     summary="获取任务列表",
     *     description="获取任务列表",
     *     tags={"后台活动"},
     *     security={
     *         {"sessid": {}},
     *     },
     *     @OA\Response(
     *         response="200",
     *         description="状态码、提示消息",
     *         @OA\JsonContent(
     *             type="object",
     *             @OA\Property(property="iRet", type="integer", description="状态码"),
     *             @OA\Property(property="sMsg", type="string", description="提示消息"),
     *             @OA\Property(property="data", type="array", 
     *                 @OA\Items(ref="#/components/schemas/TaskListItem"),
     *                 description="任务列表"
     *             )
     *         )
     *     )
     * )
     */
    public function actionTaskList()
    {
        $this->success($this->service->getTaskList());
    }
    
    /**
     * @OA\Post(
     *     path="/back/member-activity/create-module-relation",
     *     summary="新增活动模块",
     *     description="新增活动模块",
     *     tags={"后台活动"},
     *     security={
     *         {"sessid": {}},
     *     },
     *     @OA\RequestBody(
     *         @OA\MediaType(mediaType="application/x-www-form-urlencoded",
     *             @OA\Schema(
     *                 allOf={
     *                     @OA\Schema(ref="#/components/schemas/BaseRequest"),
     *                 },
     *                 @OA\Property(property="module_res_id", type="integer", description="模块资源ID"),
     *                 @OA\Property(property="activity_id", type="integer", description="活动ID"),
     *                 @OA\Property(property="module_title", type="string", description="模块标题"),
     *                 @OA\Property(property="module_summary", type="string", description="模块文案"),
     *                 @OA\Property(property="module_start_time", type="integer", description="模块外显开始时间"),
     *                 @OA\Property(property="module_end_time", type="integer", description="模块外显结束时间"),
     *                 @OA\Property(property="module_data", type="string", description="模块数据"),
     *                 required={"module_res_id", "activity_id", "module_title", "module_start_time", "module_end_time"}
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response="200",
     *         description="状态码、提示消息",
     *         @OA\JsonContent(
     *             type="object",
     *             @OA\Property(property="iRet", type="integer", description="状态码"),
     *             @OA\Property(property="sMsg", type="string", description="提示消息")
     *         )
     *     )
     * )
     */
    public function actionCreateModuleRelation()
    {
        try {
            $post = \Yii::$app->request->post();
            $form = new MemberActifityModuleRelationForm();
            $form->setScenario('create');
            if (! ($form->load($post, '') && $form->validate())) {
                $errors = $form->firstErrors;
                $this->error(array_shift($errors));
            }

            list($status, $moduleRelationId, $msg) = $this->service->createModuleRelation($form->toArray());
            if (! $status) {
                throw new BusinessException($msg);
            }
            $this->success(['module_relation_id' => (int) $moduleRelationId]);
        } catch (\Throwable $e) {
            $this->error($e->getMessage());
        }
    }

    /**
     * @OA\Post(
     *     path="/back/member-activity/update-module-relation",
     *     summary="更新活动模块",
     *     description="更新活动模块",
     *     tags={"后台活动"},
     *     security={
     *         {"sessid": {}},
     *     },
     *     @OA\RequestBody(
     *         @OA\MediaType(mediaType="application/x-www-form-urlencoded",
     *             @OA\Schema(
     *                 allOf={
     *                     @OA\Schema(ref="#/components/schemas/BaseRequest"),
     *                 },
     *                 @OA\Property(property="module_res_id", type="integer", description="模块资源ID"),
     *                 @OA\Property(property="activity_id", type="integer", description="活动ID"),
     *                 @OA\Property(property="module_relation_id", type="integer", description="活动模块ID"),
     *                 @OA\Property(property="module_title", type="string", description="模块标题"),
     *                 @OA\Property(property="module_summary", type="string", description="模块文案"),
     *                 @OA\Property(property="module_start_time", type="integer", description="模块外显开始时间"),
     *                 @OA\Property(property="module_end_time", type="integer", description="模块外显结束时间"),
     *                 @OA\Property(property="module_data", type="string", description="模块数据"),
     *                 required={"module_res_id", "activity_id", "module_relation_id", "module_title", "module_start_time", "module_end_time"}
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response="200",
     *         description="状态码、提示消息",
     *         @OA\JsonContent(
     *             type="object",
     *             @OA\Property(property="iRet", type="integer", description="状态码"),
     *             @OA\Property(property="sMsg", type="string", description="提示消息")
     *         )
     *     )
     * )
     */
    public function actionUpdateModuleRelation()
    {
        try {
            $post = \Yii::$app->request->post();
            $form = new MemberActifityModuleRelationForm();
            $form->setScenario('update');
            if (! ($form->load($post, '') && $form->validate())) {
                $errors = $form->firstErrors;
                $this->error(array_shift($errors));
            }

            list($status, $msg) = $this->service->updateModuleRelation($form->toArray());
            if (! $status) {
                throw new BusinessException($msg);
            }
            $this->success();
        } catch (\Throwable $e) {
            $this->error($e->getMessage());
        }
    }

    /**
     * @OA\Post(
     *     path="/back/member-activity/delete-module-relation",
     *     summary="删除活动模块",
     *     description="删除活动模块",
     *     tags={"后台活动"},
     *     security={
     *         {"sessid": {}},
     *     },
     *     @OA\RequestBody(
     *         @OA\MediaType(mediaType="application/x-www-form-urlencoded",
     *             @OA\Schema(
     *                 @OA\Property(property="module_relation_ids", type="string", description="模块关联ID，多个ID用逗号分隔"),
     *                 required={"module_relation_ids"}
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response="200",
     *         description="状态码、提示消息",
     *         @OA\JsonContent(
     *             type="object",
     *             @OA\Property(property="iRet", type="integer", description="状态码"),
     *             @OA\Property(property="sMsg", type="string", description="提示消息")
     *         )
     *     )
     * )
     */
    public function actionDeleteModuleRelation()
    {
        try {
            $post = \Yii::$app->request->post();
            $ids = $post['module_relation_ids'] ?? '';
            if (empty($ids)) {
                return $this->error('缺少module_relation_ids字段');
            }
            $ids = explode(',', (string) $ids);
            $ids = array_unique($ids);
            list($status, $msg) = $this->service->deleteModuleRelation($ids);
            if (! $status) {
                throw new BusinessException($msg);
            }
            $this->success();
        } catch (\Throwable $e) {
            $this->error($e->getMessage());
        }
    }

    /**
     * @OA\Post(
     *     path="/back/member-activity/change-module-goods-status",
     *     summary="更新活动商品状态",
     *     description="更新活动商品状态",
     *     tags={"后台活动"},
     *     security={
     *         {"sessid": {}},
     *     },
     *     @OA\RequestBody(
     *         @OA\MediaType(mediaType="application/x-www-form-urlencoded",
     *             @OA\Schema(
     *                 allOf={
     *                     @OA\Schema(ref="#/components/schemas/BaseRequest"),
     *                 },
     *                 @OA\Property(property="id", type="integer", description="活动商品ID"),
     *                 @OA\Property(property="status", type="integer", description="商品状态值"),
     *                 required={"id", "status"}
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response="200",
     *         description="状态码、提示消息",
     *         @OA\JsonContent(
     *             type="object",
     *             @OA\Property(property="iRet", type="integer", description="状态码"),
     *             @OA\Property(property="sMsg", type="string", description="提示消息")
     *         )
     *     )
     * )
     */
    public function actionChangeModuleGoodsStatus()
    {
        try {
            $post = \Yii::$app->request->post();
            list($status, $msg) = $this->service->changeModuleGoodsStatus($post);
            if (! $status) {
                throw new BusinessException($msg);
            }
            $this->success();
        } catch (\Throwable $e) {
            $this->error($e->getMessage());
        }
    }


    /**
     * @OA\Post(
     *     path="/back/member-activity/import-red-envelopes",
     *     summary="导入红包数据",
     *     description="导入红包数据",
     *     tags={"后台活动"},
     *     security={
     *         {"sessid": {}},
     *     },
     *     @OA\RequestBody(
     *         @OA\MediaType(
     *             mediaType="multipart/form-data",
     *             @OA\Schema(
     *                 allOf={
     *                     @OA\Schema(ref="#/components/schemas/BaseRequest"),
     *                 },
     *                 @OA\Property(property="id", type="integer", description="活动商品ID"),
     *                 @OA\Property(property="file", type="string", format="binary", description="Excel文件"),
     *                 required={"id", "file"}
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response="200",
     *         description="状态码、提示消息",
     *         @OA\JsonContent(
     *             type="object",
     *             @OA\Property(property="iRet", type="integer", description="状态码"),
     *             @OA\Property(property="sMsg", type="string", description="提示消息"),
     *             @OA\Property(property="data", type="object", description="数据")
     *         )
     *     )
     * )
     */
    public function actionImportRedEnvelopes()
    {
        try {
            $acRelationId = \Yii::$app->request->post('id') ?? 0;
            $file = $_FILES['file'] ?? "";

            list($status, $msg) = $this->service->drawActivity($acRelationId, $file);
            if (!$status) {
                throw new BusinessException($msg);
            }
            $this->success();
        } catch (\Throwable $e) {
            $this->error($e->getMessage());
        }
    }

    /**
     * @OA\Post(
     *     path="/back/member-activity/template",
     *     summary="获取红包上传模板",
     *     description="获取红包上传模板",
     *     tags={"后台活动"},
     *     security={
     *         {"sessid": {}},
     *     },
     *     @OA\RequestBody(
     *         @OA\MediaType(
     *             mediaType="application/x-www-form-urlencoded",
     *             @OA\Schema(
     *                 allOf={
     *                     @OA\Schema(ref="#/components/schemas/BaseRequest"),
     *                 }
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response="200",
     *         description="状态码、提示消息",
     *         @OA\JsonContent(
     *             type="object",
     *             @OA\Property(property="iRet", type="integer", description="状态码"),
     *             @OA\Property(property="sMsg", type="string", description="提示消息"),
     *             @OA\Property(property="data", type="object", description="数据")
     *         )
     *     )
     * )
     */
    public function actionTemplate()
    {
        $data = [
                'url'=>'https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/files/202506/红包上传模版.xlsx'
        ];
        $this->success($data);
    }
}