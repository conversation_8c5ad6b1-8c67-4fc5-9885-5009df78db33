<?php
/**
 * Author :CP
 */

namespace app\modules\wares\models;

use app\components\AppWRedisKeys;
use app\models\by;
use app\models\CUtil;
use app\modules\back\services\CouponService;
use app\modules\main\models\CommModel;
use yii\db\Exception;

class GoodsPointsModel extends CommModel
{

    public $tb_fields = [
        'id', 'gid', 'name', 'type','subtype', 'cover_image', 'images', 'mprice', 'introduce', 'label', 'position', 'is_send', 'detail', 'tab',
        'levels', 'limit_num', 'notice', 'platform', 'online_time', 'shipping', 'video', 'cover_image_3d', 'model_3d',
        'is_del', 'ctime', 'utime', 'dtime'
    ];

    public static function tbName(): string
    {
        return "`db_dreame_wares`.`t_goods_points`";
    }

    //type 商品类型
    const TYPE = [
        1 => '实体商品',
        2 => '虚拟商品',
    ];

    //label 商品标签
    const LABEL = [
        1 => '新品',
        2 => '限时',
        3 => '爆品',
    ];

    //tab 商品TAB
    const TAB = [
        1 => '新品',
        2 => 'LIFE',
        3 => 'MART',
//        4 => 'BESPOKE',
//        5 => '卡券'
    ];

    //position 商品推荐位
    const POSITION = [
        1 => '热门兑换',
        2 => '大家都在兑',
    ];

    //week 周期上架
    const WEEK = [
        1 => '周一',
        2 => '周二',
        3 => '周三',
        4 => '周四',
        5 => '周五',
        6 => '周六',
        7 => '周日',
    ];

    //是否包邮
    const SHIPPING = [
         1 => '不包邮',
         2 => '包邮'
    ];

    //是否发货
    const SEND = [
        'SHIP'    => 1,
        'NO_SHIP' => 2
    ];

    /**
     * @param $gid
     * @return string
     * 根据gid,sid获取数据
     */
    private function __getGoodsPointsByIdKey($gid): string
    {
        return AppWRedisKeys::getGoodsPointsById($gid);
    }


    /**
     * @return string
     * 获取积分商城列表
     */
    private function __getGoodsPointsList(): string
    {
        return AppWRedisKeys::getGoodsPointsList();
    }

    /**
     * @param $gid
     * 单条数据清理
     */
    private function __delIdCache($gid)
    {
        $r_key = $this->__getGoodsPointsByIdKey($gid);
        by::redis('core')->del($r_key);
    }

    /**
     * @return void
     * 清除缓存列表
     */
    private function __delListCache()
    {
        $r_key = $this->__getGoodsPointsList();
        by::redis('core')->del($r_key);
    }


    /**
     * @param $gid
     * @param bool $cache
     * @return array|\yii\db\DataReader
     * @throws Exception
     * 获取单条数据
     */
    public function GetOneById($gid, $platformIds = [], $format_price = true, bool $cache = true)
    {
        $gid = CUtil::uint($gid);

        if (empty($gid)) {
            return [];
        }

        $redis = by::redis('core');
        $r_key = $this->__getGoodsPointsByIdKey($gid);

        $aJson = $cache ? $redis->get($r_key) : false;
        $aData = (array)json_decode($aJson, true);

        if ($aJson === false) {
            $tb     = $this::tbName();
            $fields = implode(",", $this->tb_fields);
            $sql    = "SELECT {$fields} FROM  {$tb} WHERE `gid`=:gid AND `is_del` = 0 LIMIT 1";
            $aData  = by::dbMaster()->createCommand($sql, [':gid' => $gid])->queryOne();
            $aData  = $aData ?: [];
            $redis->set($r_key, json_encode($aData), ['EX' => empty($aData) ? 10 : 1800]);
        }

        if (empty($aData)) {
            return [];
        }
        //格式化价格
        if ($format_price) {
            $aData['mprice'] = CUtil::totalFee($aData['mprice'], 1);
        }

        //处理封面图
        $aData['cover_image'] = empty($aData['cover_image'] ?? '') ? [] : json_decode($aData['cover_image'], true);
        $coverImages = empty($aData['cover_image']) ? [] : array_column($aData['cover_image'], 'image', 'platform');
        !empty($platformIds) && $coverImages && $aData['cover_image'] = $coverImages[$platformIds[0] ?? 1] ?? '';
        $aData['market_image'] = $aData['cover_image'] ?? "";

        //处理notice
        $aData['notice'] = empty($aData['notice'] ?? '') ? [] : json_decode($aData['notice'], true);

        return $aData;
    }

    /**
     * @param array $aData
     * @return array
     * @throws Exception
     * 增加积分商品
     */
    public function SaveLog(array $aData): array
    {
        $gid    = CUtil::uint($aData['gid'] ?? 0);
        $mprice = $aData['mprice'] ?? 0;
        $mprice = sprintf("%.2f", $mprice);

        if (empty($gid)) {
            return [false, "增加积分商品-缺少必要参数"];
        }

        $aLog = $this->GetOneById($gid);
        if (!empty($aLog)) {
            return [false, '该积分商品已存在！'];
        }

        //替换掉所有js标签
        $detail    = $aData['detail'] ?? "";
        $notice    = $aData['notice'] ?? "";
        $introduce = $aData['introduce'] ?? "";
        $detail    = preg_replace("/<script[\s\S]*?<\/script>/i", "", $detail); //防注入
        $notice    = preg_replace("/<script[\s\S]*?<\/script>/i", "", $notice);
        $introduce = preg_replace("/<script[\s\S]*?<\/script>/i", "", $introduce);


        $save = [
            'gid'            => $gid,
            'mprice'         => CUtil::totalFee($mprice),
            'name'           => trim($aData['name'] ?? ''),
            'type'           => CUtil::uint($aData['type'] ?? '1'),
            'images'         => trim($aData['images'] ?? ''),
            'cover_image'    => trim($aData['cover_image'] ?? ''),
            'cover_image_3d' => trim($aData['cover_image_3d'] ?? ''),
            'model_3d'       => trim($aData['model_3d'] ?? ''),
            'video'          => trim($aData['video'] ?? ''),
            'introduce'      => $introduce,
            'label'          => trim($aData['label'] ?? ''),
            'tab'            => trim($aData['tab'] ?? ''),
            'is_send'        => CUtil::uint($aData['is_send'] ?? '1'),
            'detail'         => $detail,
            'levels'         => trim($aData['levels'] ?? ''),
            'limit_num'      => CUtil::uint($aData['limit_num'] ?? ''),
            'platform'       => CUtil::uint($aData['platform'] ?? '99'),
            'shipping'       => CUtil::uint($aData['shipping'] ?? '1'),
            'position'       => trim($aData['position'] ?? '0'),
            'notice'         => $notice,
            'online_time'    => trim($aData['online_time'] ?? intval(START_TIME)),
            'ctime'          => intval(START_TIME),
            'utime'          => intval(START_TIME),
            'subtype'        => CUtil::uint($aData['subtype'] ?? 0),
        ];


        $tb  = self::tbName();
        $ret = by::dbMaster()->createCommand()->insert($tb, $save)->execute();
        $id  = by::dbMaster()->getLastInsertID();

        if (!$ret) {
            return [false, "未知原因，属性sku新增失败"];
        }

        $this->__delIdCache($gid);
        $this->__delListCache();

        return [true, 'OK'];
    }


    /**
     * @param $gid
     * @param array $update
     * @return array
     * @throws Exception
     * 更新积分商品详情页
     */
    public function UpdateLog($gid, array $update): array
    {
        //允许修改的字段
        $allowed = [
            'name', 'type', 'cover_image', 'images', 'mprice', 'introduce', 'label', 'position', 'is_send', 'detail', 'tab',
            'levels', 'limit_num', 'notice', 'platform', 'online_time', 'shipping', 'video', 'cover_image_3d', 'model_3d',
            'is_del', 'utime', 'dtime','subtype'
        ];

        foreach ($update as $field => $val) {
            if (!in_array($field, $allowed)) {
                unset($update[$field]);
            }
        }

        if (empty($update)) {
            return [false, '无字段更改'];
        }

        if (isset($update['mprice'])) {
            $update['mprice'] = by::Gtype0()->totalFee($update['mprice']);
        }

        $gid  = CUtil::uint($gid);
        $aLog = $this->GetOneById($gid);
        if (empty($aLog)) {
            return [false, '无字段更改(1)'];
        }

        $tb = self::tbName();

        by::dbMaster()->createCommand()->update($tb, $update, ['gid' => $gid])->execute();

        $this->__delIdCache($gid);
        $this->__delListCache();

        return [true, "成功"];
    }


    /**
     * @param $data
     * @return array
     * 检验参数
     */
    public function checkPoints($data): array
    {
        $coverImage = $data['cover_image'] ?? '';
        $arr        = json_decode($coverImage, true);
        if (!is_array($arr)) {
            return [false, "封面图传参错误！"];
        }
        $notice    = $data['notice'] ?? '';
        $noticeArr = json_decode($notice, true);
        if (!is_array($noticeArr)) {
            return [false, "兑换须知传参错误！"];
        }
        $checkArr = ['type', 'label', 'tab', 'position', 'levels'];
        foreach ($checkArr as $keyword) {
            $arr = [];
            switch ($keyword) {
                case 'type':
                    $arr = self::TYPE;
                    break;
                case 'label':
                    $arr = self::LABEL;
                    break;
                case 'tab':
                    $arr = self::TAB;
                    break;
                case 'position':
                    $arr = self::POSITION;
                    break;
                case 'levels':
                    $arr = by::users()::USER_LEVEL;
                    break;
                default:
                    ;
            }
            if (isset($data[$keyword]) && !empty($data[$keyword])) {
                $wordArr = explode('|', $data[$keyword]);
                foreach ($wordArr as $word) {
                    if (!isset($arr[$word])) {
                        return [false, $keyword . '枚举类型有误'];
                    }
                }
            }
        }

        //上架周期传参校验
        $onlineTime = $data['online_time'] ?? '';
        $online     = CUtil::uint($data['online'] ?? 0);
        if ($online == 2 && empty($onlineTime)) {
            return [false, '周期上架时间必填'];
        }

        if ($online == 2 && intval($onlineTime) > 0 && !strpos($onlineTime, '|') && strlen($onlineTime) > 8 && intval($onlineTime) < intval(START_TIME)) {
            return [false, '周期上架时间不能小于当前时间'];
        }

        if ($onlineTime) {
            if (strlen($onlineTime) < 2 || strpos($onlineTime, '|') !== false) {
                $wordArr = explode('|', $onlineTime);
                $arr     = self::WEEK;
                foreach ($wordArr as $word) {
                    if (!isset($arr[$word])) {
                        return [false, $keyword . '枚举类型有误'];
                    }
                }
            } else {
                $onlineTime = CUtil::uint($onlineTime);
                if (empty($onlineTime) || empty(date('Y-m-d H:i:s', $onlineTime))) return [false, '周期上架时间有误'];
            }
        }

        $notice = $data['notice'] ?? '';
        if ($notice) {
            $notice = json_decode($notice, true);
            if (!is_array($notice)) {
                return [false, "兑换须知格式错误"];
            }
        }

        // 优惠券code校验
        if (isset($data['subtype'])&&intval($data['subtype'])>0) {
            list($coupon_status,$msg) = CouponService::getInstance()->check($data['subtype'], $data['sku']);
            if (!$coupon_status) {
                return [false,  $msg];
            }
        }

        return [true, $data];
    }


    /**
     * @param $input
     * @param $page
     * @param $page_size
     * @return array
     * @throws Exception
     * 获取列表
     */
    public function GetList($input, $page, $page_size = 50)
    {
        $r_key   = $this->__getGoodsPointsList();
        $sub_key = CUtil::getAllParams(__FUNCTION__, serialize($input), $page, $page_size);
        $aJson   = by::redis('core')->hGet($r_key, $sub_key);
        $aData   = (array)json_decode($aJson, true);
        if ($aJson === false) {
            $tb = $this->tbName();
            list($where, $params) = $this->__getCondition($input);

            $limitStr = '';
            if (!empty($page_size)) {
                list($offset) = CUtil::pagination($page, $page_size);
                $limitStr .= " LIMIT {$offset},{$page_size}";
            }

            $sql = "SELECT `id`,`gid` FROM {$tb} WHERE {$where} ORDER BY `id` DESC " . $limitStr;

            $aData = by::dbMaster()->createCommand($sql, $params)->queryAll();

            by::redis('core')->hSet($r_key, $sub_key, json_encode($aData));
            CUtil::ResetExpire($r_key, empty($aData) ? 10 : 6000);
        }

        return empty($aData) ? [] : $aData;
    }


    /**
     * @param $input
     * @return array
     * 查询条件约束
     */
    private function __getCondition($input): array
    {
        //SQL初始化条件
        $where             = "is_del=:is_del";
        $params[':is_del'] = 0;


        if (!empty($input['name'])) {
            $where           .= " AND `name` LIKE :name";
            $params[":name"] = "%{$input['name']}%";
        }


        if (!empty($input['type'])) {
            $where           .= " AND `type`=:type";
            $params[":type"] = CUtil::uint($input['type']);
        }

        if (!empty($input['is_send'])) {
            $where              .= " AND `is_send`=:is_send";
            $params[":is_send"] = CUtil::uint($input['is_send']);
        }

        return [$where, $params];
    }
}
