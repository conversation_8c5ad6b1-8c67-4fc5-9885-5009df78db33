<?php

namespace app\modules\wares\models;


use app\components\AppNRedisKeys;
use app\models\by;
use app\models\CUtil;
use app\modules\main\models\CommModel;
use RedisException;
use yii\db\Query;

class WaresCartModel extends CommModel
{

    public $tb_fields = [
            'id', 'user_id', 'gid', 'sid', '', 'num', 'ctime', 'utime'
    ];

    public static function tbName($userId): string
    {
        // 计算分表索引（0-9）
        $tableIndex = abs(intval($userId)) % 10;

        // 返回正确的表名格式
        return "`db_dreame_wares`.`wares_cart_{$tableIndex}`";
    }

    private function WaresCartCache($userId): string
    {
        // 这里可以实现缓存逻辑
        return AppNRedisKeys::WaresCartCache($userId);
    }

    /**
     * @throws RedisException
     * 清除用户购物车缓存
     */
    public function clearCartCache($userId)
    {
        $redis = by::redis();
        $key   = $this->WaresCartCache($userId);
        if ($redis->exists($key)) {
            $redis->del($key);
        }
        // 清除缓存后，可能需要重新加载数据
        CUtil::debug("清除用户{$userId}购物车缓存成功", 'info.wares_cart_model');
    }

    public function getCartGoodsNum($user_id): array
    {
        try {
            // 确定分表
            $table = self::tbName($user_id);

            // 使用QueryBuilder构建查询，提高安全性和可读性
            $query = new Query();
            $items = $query->select(['gid', 'sid', 'num'])
                    ->from($table)
                    ->where(['user_id' => $user_id])
                    ->createCommand(by::dbMaster())
                    ->queryAll();

            // 使用array_reduce高效处理结果集
            return array_reduce($items, function (array $result, array $item) {
                $result[$item['gid']][$item['sid']] = $item['num'];
                return $result;
            }, []);
        } catch (\Exception $e) {
            // 记录异常信息
            $error = $e->getMessage() . "|" . $e->getFile() . ":" . $e->getLine();
            CUtil::debug($error, 'error.wares_cart_model');
            return [];
        }
    }

    /**
     * 批量保存数据到购物车表，存在则更新数量
     *
     * @param string $userId 用户ID
     * @param array $rows 数据行数组
     * @param null $transaction 外部事务（可选）
     * @return bool 是否执行成功
     */
    public function batchSaveData(string $userId, array $rows, $transaction = null): bool
    {
        if (empty($rows)) {
            return false;
        }

        $table   = static::tbName($userId);
        $columns = ['user_id', 'gid', 'sid', 'num', 'ctime'];
        $columns = implode("`,`", $columns);
        $db      = by::dbMaster();

        try {
            // 使用事务包裹所有插入操作
            $transaction = $transaction ?? $db->beginTransaction();

            foreach ($rows as $rowData) {
                // 提取必要字段并过滤
                $user_id = $userId;
                $gid     = $rowData['gid'] ?? 0;
                $sid     = $rowData['sid'] ?? 0;
                $num     = (int) ($rowData['num'] ?? 0);
                $ctime   = time();

                // 使用参数化查询防止SQL注入
                $sql = "INSERT INTO {$table} (`{$columns}`)
                    VALUES (:user_id, :gid, :sid, :num, :ctime)
                    ON DUPLICATE KEY UPDATE `num` = `num` + :increment_num";

                $command = $db->createCommand($sql, [
                        ':user_id'       => $user_id,
                        ':gid'           => $gid,
                        ':sid'           => $sid,
                        ':num'           => $num,
                        ':ctime'         => $ctime,
                        ':increment_num' => $num
                ]);

                $command->execute();
            }

            // 如果是内部事务则提交
            if (!$transaction->isActive) {
                $transaction->commit();
            }

            return true;
        } catch (\Exception $e) {
            // 回滚内部事务
            if (isset($transaction) && $transaction->isActive) {
                $transaction->rollBack();
            }

            CUtil::debug("批量保存数据失败: " . $e->getMessage(), 'error.wares_cart_model');
            return false;
        }
    }

    /**
     * 获取购物车商品总数量
     * @param int $userId
     * @return int
     */
    public function getCartCount($userId): int
    {
        try {

            // 添加缓存
            $redis = by::redis();
            $key   = $this->WaresCartCache($userId);

            if ($redis->hexists($key, 'total_items')) {
                return intval($redis->hget($key, 'total_items'));
            }

            $table = self::tbName($userId);
            $query = new Query();
            $count = $query->select(['SUM(num) as total'])
                    ->from($table)
                    ->where(['user_id' => $userId])
                    ->createCommand(by::dbMaster())
                    ->queryScalar();

            $count= intval($count ?? 0);
            $redis->hset($key, 'total_items', $count);
            $redis->expire($key,  3600);

            return $count;
        } catch (\Exception $e) {
            $error = $e->getMessage() . "|" . $e->getFile() . ":" . $e->getLine();
            CUtil::debug($error, 'error.wares_cart_model');
            return 0;
        }
    }

    /**
     * 获取购物车列表
     * @param int $userId
     * @param int $page 页码
     * @param int $pageSize 每页数量
     * @return array
     */
    public function getCartList($userId, $page = 1, $pageSize = 20): array
    {
        try {
            // 计算分页参数
            $offset = ($page - 1) * $pageSize;

            // 添加缓存
            $redis = by::redis();
            $key   = $this->WaresCartCache($userId);

            // 获取总数的缓存
            $totalKey = "total";
            $pageKey  = "{$page}_{$pageSize}";

            // 检查是否存在缓存
            if ($redis->hexists($key, $totalKey) && $redis->hexists($key, $pageKey)) {
                $total = intval($redis->hget($key, $totalKey));
                $items = json_decode($redis->hget($key, $pageKey), true);

                return [
                        'list'  => $items ?: [],
                        'total' => $total
                ];
            }

            $table = self::tbName($userId);
            $query = new Query();

            // 获取总数
            $total = $this->getCartCount($userId);

            // 获取分页数据
            $items = $query->select(['id', 'gid', 'sid', 'num', 'ctime'])
                    ->from($table)
                    ->where(['user_id' => $userId])
                    ->orderBy(['id' => SORT_DESC])
                    ->offset($offset)
                    ->limit($pageSize)
                    ->createCommand(by::dbMaster())
                    ->queryAll();

            // 使用hash结构缓存数据
            $redis->hset($key, $totalKey, $total);
            $redis->hset($key, $pageKey, json_encode($items));

            // 设置过期时间
            $redis->expire($key,  3600);

            return [
                    'list'  => $items ?: [],
                    'total' => $total
            ];
        } catch (\Exception $e) {
            $error = $e->getMessage() . "|" . $e->getFile() . ":" . $e->getLine();
            CUtil::debug($error, 'error.wares_cart_model');
            return [
                    'list'  => [],
                    'total' => 0
            ];
        }
    }

    /**
     * 修改购物车商品数量
     * @param int $userId
     * @param int $cartId
     * @param int $num
     * @return bool
     */
    public function modifyCartNum($userId, $cartId, $num): bool
    {
        try {
            $table  = self::tbName($userId);
            $result = by::dbMaster()->createCommand()->update(
                    $table,
                    ['num' => $num, 'utime' => intval(START_TIME)],
                    ['id' => $cartId, 'user_id' => $userId]
            )->execute();

            return $result > 0;
        } catch (\Exception $e) {
            $error = $e->getMessage() . "|" . $e->getFile() . ":" . $e->getLine();
            CUtil::debug($error, 'error.wares_cart_model');
            return false;
        }
    }

    /**
     * 删除购物车商品
     * @param int $userId
     * @param array $cartIds
     * @return bool
     */
    public function deleteCartItems($userId, array $cartIds): bool
    {
        if (empty($cartIds)) {
            return false;
        }

        try {
            $table  = self::tbName($userId);
            $result = by::dbMaster()->createCommand()->delete(
                    $table,
                    ['and', ['user_id' => $userId], ['in', 'id', $cartIds]]
            )->execute();

            return $result > 0;
        } catch (\Exception $e) {
            $error = $e->getMessage() . "|" . $e->getFile() . ":" . $e->getLine();
            CUtil::debug($error, 'error.wares_cart_model');
            return false;
        }
    }

}
