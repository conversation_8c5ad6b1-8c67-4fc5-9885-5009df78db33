# 团购自动创建新团功能实现说明

## 功能概述

当用户尝试加入一个已满员的团购时，系统会自动：
1. 查找同一活动和商品下其他可加入的团购
2. 如果找到可加入的团，自动将用户加入该团
3. 如果没有找到可加入的团，自动为用户创建新团
4. 更新订单的 `group_purchase_id` 为新的团购ID

## 修改的文件

### 1. `modules/goods/models/GroupPurchase/GroupPurchaseModel.php`

**新增方法：**
- `findAvailableGroupPurchase(int $activityId, int $gid, int $excludeGroupId = 0): ?array`

**功能：**
- 查找指定活动和商品下可加入的团购
- 排除当前已满员的团
- 检查每个团的实际成员数量
- 返回第一个未满员的团购信息

**参数：**
- `$activityId`: 活动ID
- `$gid`: 商品ID  
- `$excludeGroupId`: 要排除的团购ID（通常是当前已满员的团）

**返回值：**
- 成功：返回可加入的团购信息数组
- 失败：返回 `null`

### 2. `jobs/GroupPurchaseJob.php`

#### 修改的方法：

**`checkGroupPurchaseEligibility()`**
- 在检测到团已满员时，不再直接返回失败
- 调用 `findAvailableGroupPurchase()` 查找可加入的团
- 如果找到可加入的团，更新订单信息并返回成功
- 如果没有找到，调用 `createNewGroupPurchase()` 创建新团

**`execute()`**
- 处理新的返回值结构
- 当切换到新团时，重新获取团长信息

**`updateOrderGroupInfo()`**
- 更新方法注释，明确其用途是更新订单的团购信息
- 支持传入新的团购ID

#### 新增方法：

**`createNewGroupPurchase(int $activityId, int $gid): int`**
- 使用 `GroupPurchaseService::initiateGroupPurchase()` 创建新团
- 处理创建过程中的异常
- 返回新创建的团购ID

## 业务流程

```
用户尝试加入团购
    ↓
检查团是否满员
    ↓
[满员] → 查找可加入的团
    ↓
[找到] → 更新订单团购ID → 加入新团 → 成功
    ↓
[未找到] → 创建新团 → 更新订单团购ID → 加入新团 → 成功
    ↓
[未满员] → 直接加入当前团 → 成功
```

## 关键逻辑说明

### 1. 团满员检测
```php
if ($membersCount >= $maxMembers) {
    // 团已满员，启动自动处理逻辑
}
```

### 2. 查找可加入的团
```php
$availableGroup = byNew::GroupPurchaseModel()->findAvailableGroupPurchase(
    $leader['activity_id'], 
    $orderGoodsId, 
    $leader['id']
);
```

### 3. 创建新团
```php
$newGroupId = $this->createNewGroupPurchase($leader['activity_id'], $orderGoodsId);
```

### 4. 更新订单信息
```php
$this->updateOrderGroupInfo($newGroupId);
```

## 返回值结构

修改后的 `checkGroupPurchaseEligibility()` 方法返回值：

```php
// 成功加入（原团或新团）
[
    'canJoin' => true, 
    'needUpdateOrder' => false,
    'newGroupId' => $newGroupId  // 可选，仅在切换团时存在
]

// 无法加入
[
    'canJoin' => false, 
    'needUpdateOrder' => true
]
```

## 日志记录

新增的日志记录点：
- 团满员时的处理开始
- 找到可加入的团
- 没有找到可加入的团，开始创建新团
- 成功创建新团
- 创建新团失败
- 订单团购信息更新

## 异常处理

1. **查找可加入团时的异常**：返回 `null`，继续创建新团流程
2. **创建新团时的异常**：捕获异常，记录日志，返回失败
3. **更新订单信息时的异常**：由调用方的事务处理

## 测试建议

1. **团满员场景测试**：
   - 创建一个满员的团
   - 模拟用户尝试加入
   - 验证是否自动查找其他团或创建新团

2. **多团并存测试**：
   - 同一活动商品下创建多个团
   - 其中一些满员，一些未满员
   - 验证是否正确找到未满员的团

3. **创建新团测试**：
   - 确保没有可加入的团时能正确创建新团
   - 验证新团的团长信息正确

4. **订单信息更新测试**：
   - 验证订单的 `group_purchase_id` 是否正确更新
   - 验证订单缓存是否正确清理

## 注意事项

1. **并发安全**：在高并发场景下，可能需要考虑加锁机制
2. **事务一致性**：确保订单更新和团购加入在同一事务中
3. **性能优化**：查找可加入团的逻辑可能需要优化查询性能
4. **业务规则**：需要确认是否有其他业务规则限制自动创建新团
