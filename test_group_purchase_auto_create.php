<?php
/**
 * 团购自动创建新团功能测试脚本
 * 
 * 测试场景：
 * 1. 团满员时自动查找其他可加入的团
 * 2. 没有可加入的团时自动创建新团
 * 3. 更新订单的团购信息
 */

require_once __DIR__ . '/vendor/autoload.php';

use app\jobs\GroupPurchaseJob;
use app\models\byNew;

class GroupPurchaseAutoCreateTest
{
    /**
     * 测试团满员时的自动处理逻辑
     */
    public function testAutoCreateWhenGroupFull()
    {
        echo "=== 团购自动创建新团功能测试 ===\n\n";
        
        // 测试数据
        $testData = [
            'order_no' => 'TEST_ORDER_' . time(),
            'group_purchase_id' => 1, // 假设这是一个已满员的团
            'user_id' => 12345,
        ];
        
        echo "1. 测试数据准备:\n";
        echo "   订单号: {$testData['order_no']}\n";
        echo "   团购ID: {$testData['group_purchase_id']}\n";
        echo "   用户ID: {$testData['user_id']}\n\n";
        
        // 创建Job实例
        $job = new GroupPurchaseJob();
        $job->order_no = $testData['order_no'];
        $job->group_purchase_id = $testData['group_purchase_id'];
        $job->user_id = $testData['user_id'];
        
        echo "2. 测试查找可加入的团购功能:\n";
        $this->testFindAvailableGroup();
        
        echo "\n3. 测试创建新团功能:\n";
        $this->testCreateNewGroup();
        
        echo "\n4. 测试更新订单团购信息功能:\n";
        $this->testUpdateOrderGroupInfo();
        
        echo "\n=== 测试完成 ===\n";
    }
    
    /**
     * 测试查找可加入的团购
     */
    private function testFindAvailableGroup()
    {
        try {
            $groupModel = byNew::GroupPurchaseModel();
            
            // 测试参数
            $activityId = 1;
            $gid = 100;
            $excludeGroupId = 1;
            
            echo "   查找活动ID: {$activityId}, 商品ID: {$gid} 的可加入团购...\n";
            
            $availableGroup = $groupModel->findAvailableGroupPurchase($activityId, $gid, $excludeGroupId);
            
            if ($availableGroup) {
                echo "   ✓ 找到可加入的团购: ID = {$availableGroup['id']}\n";
                echo "     团长用户ID: {$availableGroup['user_id']}\n";
                echo "     当前状态: {$availableGroup['status']}\n";
            } else {
                echo "   ✓ 没有找到可加入的团购，需要创建新团\n";
            }
        } catch (Exception $e) {
            echo "   ✗ 测试失败: " . $e->getMessage() . "\n";
        }
    }
    
    /**
     * 测试创建新团
     */
    private function testCreateNewGroup()
    {
        try {
            echo "   模拟创建新团购流程...\n";
            
            // 这里只是模拟，实际创建需要有效的活动和商品数据
            $activityId = 1;
            $gid = 100;
            $userId = 12345;
            
            echo "   ✓ 创建新团参数: 活动ID={$activityId}, 商品ID={$gid}, 用户ID={$userId}\n";
            echo "   ✓ 新团创建逻辑已集成到 GroupPurchaseJob::createNewGroupPurchase() 方法中\n";
            
        } catch (Exception $e) {
            echo "   ✗ 测试失败: " . $e->getMessage() . "\n";
        }
    }
    
    /**
     * 测试更新订单团购信息
     */
    private function testUpdateOrderGroupInfo()
    {
        try {
            echo "   模拟更新订单团购信息...\n";
            
            $orderId = 'TEST_ORDER_' . time();
            $newGroupId = 999;
            
            echo "   ✓ 订单号: {$orderId}\n";
            echo "   ✓ 新团购ID: {$newGroupId}\n";
            echo "   ✓ 订单更新逻辑已集成到 GroupPurchaseJob::updateOrderGroupInfo() 方法中\n";
            
        } catch (Exception $e) {
            echo "   ✗ 测试失败: " . $e->getMessage() . "\n";
        }
    }
}

// 运行测试
if (php_sapi_name() === 'cli') {
    $test = new GroupPurchaseAutoCreateTest();
    $test->testAutoCreateWhenGroupFull();
}
